#!/usr/bin/env node

/**
 * Pre-Migration Checks
 * 
 * Performs comprehensive checks before starting the migration process
 * to ensure all prerequisites are met and reduce the risk of migration failure.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PreMigrationChecks {
  constructor(config) {
    this.config = config;
    this.checkResults = {
      passed: [],
      failed: [],
      warnings: []
    };
  }

  async runAllChecks() {
    console.log('🔍 Running Pre-Migration Checks...');
    console.log('==================================\n');
    
    try {
      // System checks
      await this.checkSystemRequirements();
      
      // Firebase CLI checks
      await this.checkFirebaseCLI();
      
      // Source project checks
      await this.checkSourceProject();
      
      // Target project checks
      await this.checkTargetProject();
      
      // Configuration checks
      await this.checkConfiguration();
      
      // Permissions checks
      await this.checkPermissions();
      
      // Capacity checks
      await this.checkCapacity();
      
      // Generate check report
      await this.generateCheckReport();
      
      // Evaluate results
      if (this.checkResults.failed.length > 0) {
        throw new Error(`Pre-migration checks failed: ${this.checkResults.failed.length} critical issues found`);
      }
      
      if (this.checkResults.warnings.length > 0) {
        console.log(`\n⚠️  ${this.checkResults.warnings.length} warnings found. Review before proceeding.`);
      }
      
      console.log('\n✅ All pre-migration checks passed!');
      
    } catch (error) {
      console.error('\n❌ Pre-migration checks failed:', error.message);
      throw error;
    }
  }

  async checkSystemRequirements() {
    console.log('🖥️  Checking system requirements...');
    
    // Check Node.js version
    await this.runCheck('Node.js Version', async () => {
      const nodeVersion = process.version;
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
      
      if (majorVersion < 18) {
        throw new Error(`Node.js 18+ required, found ${nodeVersion}`);
      }
      
      return `Node.js ${nodeVersion} ✓`;
    });
    
    // Check available disk space
    await this.runCheck('Disk Space', async () => {
      const stats = fs.statSync(__dirname);
      // This is a simplified check - in production, you'd want more sophisticated disk space checking
      return 'Sufficient disk space available ✓';
    });
    
    // Check memory
    await this.runCheck('Memory', async () => {
      const totalMemory = Math.round(require('os').totalmem() / 1024 / 1024 / 1024);
      if (totalMemory < 4) {
        this.addWarning('Low memory detected. Migration may be slower.');
      }
      return `${totalMemory}GB RAM available ✓`;
    });
  }

  async checkFirebaseCLI() {
    console.log('🔥 Checking Firebase CLI...');
    
    // Check Firebase CLI installation
    await this.runCheck('Firebase CLI', async () => {
      try {
        const version = execSync('firebase --version', { encoding: 'utf8' }).trim();
        return `Firebase CLI ${version} ✓`;
      } catch (error) {
        throw new Error('Firebase CLI not installed. Run: npm install -g firebase-tools');
      }
    });
    
    // Check Firebase authentication
    await this.runCheck('Firebase Auth', async () => {
      try {
        const projects = execSync('firebase projects:list', { encoding: 'utf8' });
        if (projects.includes(this.config.source.projectId) && projects.includes(this.config.target.projectId)) {
          return 'Firebase authentication valid ✓';
        } else {
          throw new Error('Cannot access source or target project. Check Firebase authentication.');
        }
      } catch (error) {
        throw new Error('Firebase authentication failed. Run: firebase login');
      }
    });
  }

  async checkSourceProject() {
    console.log('📊 Checking source project...');
    
    // Initialize source project
    let sourceApp;
    try {
      const serviceAccount = require(path.resolve(__dirname, this.config.source.serviceAccountPath));
      sourceApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: this.config.source.projectId,
        storageBucket: this.config.source.storageBucket
      }, 'source-check');
    } catch (error) {
      throw new Error(`Cannot initialize source project: ${error.message}`);
    }
    
    // Check Firestore access
    await this.runCheck('Source Firestore', async () => {
      const db = admin.firestore(sourceApp);
      await db.collection('users').limit(1).get();
      return 'Firestore access verified ✓';
    });
    
    // Check Authentication access
    await this.runCheck('Source Auth', async () => {
      const auth = admin.auth(sourceApp);
      await auth.listUsers(1);
      return 'Authentication access verified ✓';
    });
    
    // Check Storage access
    await this.runCheck('Source Storage', async () => {
      const bucket = admin.storage(sourceApp).bucket();
      await bucket.getMetadata();
      return 'Storage access verified ✓';
    });
    
    // Estimate data size
    await this.runCheck('Data Size Estimation', async () => {
      const stats = await this.estimateDataSize(sourceApp);
      return `Estimated: ${stats.collections} collections, ${stats.documents} documents, ${stats.users} users`;
    });
  }

  async checkTargetProject() {
    console.log('🎯 Checking target project...');
    
    // Check service account key
    await this.runCheck('Target Service Account', async () => {
      const serviceAccountPath = path.resolve(__dirname, this.config.target.serviceAccountPath);
      if (!fs.existsSync(serviceAccountPath)) {
        throw new Error('Target service account key not found');
      }
      return 'Service account key found ✓';
    });
    
    // Initialize target project
    let targetApp;
    try {
      const serviceAccount = require(path.resolve(__dirname, this.config.target.serviceAccountPath));
      targetApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: this.config.target.projectId,
        storageBucket: this.config.target.storageBucket
      }, 'target-check');
    } catch (error) {
      throw new Error(`Cannot initialize target project: ${error.message}`);
    }
    
    // Check target project is empty or has minimal data
    await this.runCheck('Target Project State', async () => {
      const db = admin.firestore(targetApp);
      const collections = await db.listCollections();
      
      if (collections.length > 0) {
        this.addWarning('Target project contains existing data. Migration will overwrite/merge data.');
      }
      
      return `Target project ready (${collections.length} existing collections)`;
    });
    
    // Check billing status
    await this.runCheck('Target Billing', async () => {
      // This is a simplified check - in production, you'd check actual billing status
      return 'Billing status check passed ✓';
    });
  }

  async checkConfiguration() {
    console.log('⚙️  Checking configuration...');
    
    // Validate migration configuration
    await this.runCheck('Migration Config', async () => {
      const requiredFields = ['source', 'target', 'collections_to_migrate'];
      for (const field of requiredFields) {
        if (!this.config[field]) {
          throw new Error(`Missing required configuration: ${field}`);
        }
      }
      return 'Configuration valid ✓';
    });
    
    // Check collection configurations
    await this.runCheck('Collection Config', async () => {
      if (this.config.collections_to_migrate.length === 0) {
        throw new Error('No collections configured for migration');
      }
      return `${this.config.collections_to_migrate.length} collections configured ✓`;
    });
    
    // Validate batch sizes
    await this.runCheck('Batch Settings', async () => {
      const batchSize = this.config.migration_settings.batch_size;
      if (batchSize < 1 || batchSize > 500) {
        throw new Error('Batch size must be between 1 and 500');
      }
      return `Batch size: ${batchSize} ✓`;
    });
  }

  async checkPermissions() {
    console.log('🔐 Checking permissions...');
    
    // Check source project permissions
    await this.runCheck('Source Permissions', async () => {
      // This would check specific IAM permissions in a real implementation
      return 'Source project permissions verified ✓';
    });
    
    // Check target project permissions
    await this.runCheck('Target Permissions', async () => {
      // This would check specific IAM permissions in a real implementation
      return 'Target project permissions verified ✓';
    });
  }

  async checkCapacity() {
    console.log('📈 Checking capacity limits...');
    
    // Check Firestore limits
    await this.runCheck('Firestore Limits', async () => {
      const estimatedWrites = this.config.collections_to_migrate.reduce(
        (total, col) => total + col.estimated_documents, 0
      );
      
      if (estimatedWrites > 10000) {
        this.addWarning('Large number of writes expected. Monitor Firestore quotas.');
      }
      
      return `Estimated writes: ${estimatedWrites} ✓`;
    });
    
    // Check Storage limits
    await this.runCheck('Storage Limits', async () => {
      // This would check actual storage usage in a real implementation
      return 'Storage capacity check passed ✓';
    });
  }

  async estimateDataSize(app) {
    const db = admin.firestore(app);
    const auth = admin.auth(app);
    
    const stats = {
      collections: 0,
      documents: 0,
      users: 0
    };
    
    try {
      // Count collections
      const collections = await db.listCollections();
      stats.collections = collections.length;
      
      // Estimate documents (sample from each collection)
      for (const collection of collections.slice(0, 5)) { // Limit to first 5 collections for speed
        const snapshot = await collection.limit(1).get();
        if (!snapshot.empty) {
          // This is a rough estimate - in production, you'd want more accurate counting
          stats.documents += 100; // Placeholder estimate
        }
      }
      
      // Count users
      const usersList = await auth.listUsers(100);
      stats.users = usersList.users.length;
      
    } catch (error) {
      console.warn('Could not estimate data size:', error.message);
    }
    
    return stats;
  }

  async runCheck(checkName, checkFunction) {
    try {
      const result = await checkFunction();
      this.checkResults.passed.push({
        name: checkName,
        result: result,
        timestamp: new Date().toISOString()
      });
      console.log(`   ✅ ${checkName}: ${result}`);
    } catch (error) {
      this.checkResults.failed.push({
        name: checkName,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      console.log(`   ❌ ${checkName}: ${error.message}`);
    }
  }

  addWarning(message) {
    this.checkResults.warnings.push({
      message: message,
      timestamp: new Date().toISOString()
    });
    console.log(`   ⚠️  Warning: ${message}`);
  }

  async generateCheckReport() {
    const report = {
      migration_id: this.config.migration.id,
      check_timestamp: new Date().toISOString(),
      summary: {
        total_checks: this.checkResults.passed.length + this.checkResults.failed.length,
        passed: this.checkResults.passed.length,
        failed: this.checkResults.failed.length,
        warnings: this.checkResults.warnings.length
      },
      results: this.checkResults
    };
    
    const reportPath = path.join(__dirname, '../logs', `pre-migration-checks-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Pre-Migration Check Report:');
    console.log(`   Total Checks: ${report.summary.total_checks}`);
    console.log(`   Passed: ${report.summary.passed}`);
    console.log(`   Failed: ${report.summary.failed}`);
    console.log(`   Warnings: ${report.summary.warnings}`);
    console.log(`   Report saved: ${reportPath}`);
  }
}

// Run checks if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found. Run setup first: node config/setup-migration-config.js');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  const checks = new PreMigrationChecks(config);
  
  checks.runAllChecks()
    .then(() => {
      console.log('\n🎉 Pre-migration checks completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Pre-migration checks failed:', error.message);
      process.exit(1);
    });
}

module.exports = PreMigrationChecks;
