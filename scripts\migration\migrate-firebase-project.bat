@echo off
echo ========================================
echo 🔄 FIREBASE PROJECT MIGRATION SYSTEM
echo ========================================
echo.
echo This script will guide you through the complete
echo Firebase project migration process.
echo.
echo ⚠️  IMPORTANT WARNINGS:
echo   • Ensure you have backups before proceeding
echo   • This process may take several hours
echo   • Do not interrupt the migration once started
echo   • Have rollback plan ready
echo.

pause

echo.
echo 📋 Step 1: Checking Prerequisites...
echo ========================================

REM Check Node.js version
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js 18 or higher.
    pause
    exit /b 1
)

echo ✅ Node.js found

REM Check Firebase CLI
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI not found. Installing...
    npm install -g firebase-tools
    if %errorlevel% neq 0 (
        echo ❌ Failed to install Firebase CLI
        pause
        exit /b 1
    )
)

echo ✅ Firebase CLI available

REM Check if already in migration directory
if not exist "config\migration-config.json" (
    echo 📁 Navigating to migration directory...
    cd /d "%~dp0"
    if not exist "config" (
        echo ❌ Migration system not found. Please run from correct directory.
        pause
        exit /b 1
    )
)

echo ✅ Migration system found

echo.
echo 🔧 Step 2: Configuration Setup
echo ========================================

if not exist "config\migration-config.json" (
    echo 📝 Setting up migration configuration...
    node config\setup-migration-config.js
    if %errorlevel% neq 0 (
        echo ❌ Configuration setup failed
        pause
        exit /b 1
    )
    echo ✅ Configuration setup completed
) else (
    echo ✅ Configuration already exists
    
    set /p reconfigure="Do you want to reconfigure? (y/N): "
    if /i "%reconfigure%"=="y" (
        echo 📝 Reconfiguring migration settings...
        node config\setup-migration-config.js
        if %errorlevel% neq 0 (
            echo ❌ Reconfiguration failed
            pause
            exit /b 1
        )
    )
)

echo.
echo 🔍 Step 3: Pre-Migration Checks
echo ========================================

echo 📊 Running comprehensive pre-migration validation...
node orchestration\pre-migration-checks.js
if %errorlevel% neq 0 (
    echo ❌ Pre-migration checks failed
    echo.
    echo Please review the issues and fix them before proceeding.
    echo Check the logs in the logs\ directory for details.
    pause
    exit /b 1
)

echo ✅ Pre-migration checks passed

echo.
echo 🎯 Step 4: Migration Confirmation
echo ========================================

echo.
echo 📋 Migration Summary:
echo   • Source Project: [Will be displayed from config]
echo   • Target Project: [Will be displayed from config]
echo   • Estimated Duration: 2-6 hours
echo   • Backup: Will be created automatically
echo.

set /p confirm="Are you ready to start the migration? (yes/no): "
if /i not "%confirm%"=="yes" (
    echo ❌ Migration cancelled by user
    pause
    exit /b 0
)

echo.
echo 🚀 Step 5: Executing Migration
echo ========================================

echo 📅 Migration started at: %date% %time%
echo.

REM Execute the main migration orchestrator
node orchestration\migration-orchestrator.js
if %errorlevel% neq 0 (
    echo.
    echo ❌ MIGRATION FAILED!
    echo ==================
    echo.
    echo The migration process encountered an error.
    echo Please check the logs for details:
    echo   • logs\migration-final-report-*.json
    echo   • logs\*-migration-*.json
    echo.
    echo If rollback is needed, run:
    echo   node orchestration\rollback-system.js
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ MIGRATION COMPLETED SUCCESSFULLY!
echo ===================================

echo.
echo 🔍 Step 6: Post-Migration Verification
echo ========================================

set /p verify="Do you want to run post-migration verification? (Y/n): "
if /i not "%verify%"=="n" (
    echo 📊 Running post-migration verification...
    node verification\data-integrity-checker.js
    if %errorlevel% neq 0 (
        echo ⚠️  Verification found issues. Please review the report.
    ) else (
        echo ✅ Verification completed successfully
    )
)

echo.
echo 📱 Step 7: Update App Configuration
echo ========================================

set /p updateConfig="Do you want to update Flutter app configuration? (Y/n): "
if /i not "%updateConfig%"=="n" (
    echo 🔧 Updating Flutter app configuration...
    node config\flutter-config-updater.js
    if %errorlevel% neq 0 (
        echo ⚠️  Configuration update had issues. Please review manually.
    ) else (
        echo ✅ Configuration updated successfully
    )
)

echo.
echo 🎉 MIGRATION PROCESS COMPLETED!
echo ===============================
echo.
echo 📋 Next Steps:
echo   1. Review migration reports in logs\ directory
echo   2. Download actual config files from Firebase Console
echo   3. Replace template files with downloaded versions
echo   4. Update SHA-1 certificates for Android
echo   5. Test app functionality thoroughly
echo   6. Update CI/CD pipelines with new project
echo   7. Monitor system performance for 48 hours
echo.
echo 📚 Documentation:
echo   • docs\MIGRATION_GUIDE.md - Complete migration guide
echo   • docs\TROUBLESHOOTING.md - Troubleshooting help
echo   • docs\VERIFICATION_CHECKLIST.md - Verification steps
echo.
echo 📞 Support:
echo   If you encounter issues, check the troubleshooting guide
echo   or contact the technical team.
echo.

REM Open logs directory for review
set /p openLogs="Do you want to open the logs directory? (Y/n): "
if /i not "%openLogs%"=="n" (
    start explorer logs
)

echo.
echo 🔄 Migration completed at: %date% %time%
echo.
echo Thank you for using the Firebase Migration System!
echo.

pause
