#!/usr/bin/env node

/**
 * Flutter Configuration Updater
 * 
 * Updates Flutter app configuration files for the new Firebase project
 * including firebase_options.dart and platform-specific configurations.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class FlutterConfigUpdater {
  constructor(config) {
    this.config = config;
    this.projectRoot = path.resolve(__dirname, '../../../..');
    this.backupSuffix = '.backup-' + Date.now();
  }

  async updateAllConfigurations() {
    console.log('🔄 Starting Flutter configuration update...');
    
    try {
      // Step 1: Generate new firebase_options.dart
      await this.generateFirebaseOptions();
      
      // Step 2: Update Android configuration
      await this.updateAndroidConfig();
      
      // Step 3: Update iOS configuration (if exists)
      await this.updateIOSConfig();
      
      // Step 4: Update Web configuration (if exists)
      await this.updateWebConfig();
      
      // Step 5: Update Flutter-specific configurations
      await this.updateFlutterConfigs();
      
      console.log('\n✅ Flutter configuration update completed successfully!');
      
    } catch (error) {
      console.error('\n❌ Flutter configuration update failed:', error.message);
      throw error;
    }
  }

  async generateFirebaseOptions() {
    console.log('\n🔥 Generating new firebase_options.dart...');
    
    try {
      // Check if Firebase CLI is available
      try {
        execSync('firebase --version', { stdio: 'pipe' });
      } catch (error) {
        throw new Error('Firebase CLI not found. Please install: npm install -g firebase-tools');
      }
      
      // Set the target project
      console.log(`   Setting Firebase project to: ${this.config.target.projectId}`);
      execSync(`firebase use ${this.config.target.projectId}`, {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      // Generate firebase_options.dart
      console.log('   Generating firebase_options.dart...');
      const flutterConfigCommand = 'flutterfire configure --project=' + this.config.target.projectId + ' --yes';
      
      try {
        execSync(flutterConfigCommand, {
          cwd: this.projectRoot,
          stdio: 'pipe'
        });
      } catch (error) {
        // If flutterfire is not available, create manual configuration
        console.log('   FlutterFire CLI not found, creating manual configuration...');
        await this.createManualFirebaseOptions();
      }
      
      console.log('   ✅ firebase_options.dart generated successfully');
      
    } catch (error) {
      throw new Error(`Failed to generate firebase_options.dart: ${error.message}`);
    }
  }

  async createManualFirebaseOptions() {
    const firebaseOptionsPath = path.join(this.projectRoot, 'lib', 'firebase_options.dart');
    
    // Backup existing file
    if (fs.existsSync(firebaseOptionsPath)) {
      fs.copyFileSync(firebaseOptionsPath, firebaseOptionsPath + this.backupSuffix);
    }
    
    // Create new firebase_options.dart content
    const firebaseOptionsContent = `// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// \`\`\`dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// \`\`\`
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: '${this.config.target.webApiKey || 'YOUR_WEB_API_KEY'}',
    appId: '${this.config.target.webAppId || 'YOUR_WEB_APP_ID'}',
    messagingSenderId: '${this.config.target.projectNumber}',
    projectId: '${this.config.target.projectId}',
    authDomain: '${this.config.target.projectId}.firebaseapp.com',
    storageBucket: '${this.config.target.storageBucket}',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: '${this.config.target.androidApiKey || 'YOUR_ANDROID_API_KEY'}',
    appId: '${this.config.target.androidAppId || 'YOUR_ANDROID_APP_ID'}',
    messagingSenderId: '${this.config.target.projectNumber}',
    projectId: '${this.config.target.projectId}',
    storageBucket: '${this.config.target.storageBucket}',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: '${this.config.target.iosApiKey || 'YOUR_IOS_API_KEY'}',
    appId: '${this.config.target.iosAppId || 'YOUR_IOS_APP_ID'}',
    messagingSenderId: '${this.config.target.projectNumber}',
    projectId: '${this.config.target.projectId}',
    storageBucket: '${this.config.target.storageBucket}',
    iosBundleId: '${this.config.platform_configs.ios.bundle_id}',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: '${this.config.target.iosApiKey || 'YOUR_IOS_API_KEY'}',
    appId: '${this.config.target.iosAppId || 'YOUR_IOS_APP_ID'}',
    messagingSenderId: '${this.config.target.projectNumber}',
    projectId: '${this.config.target.projectId}',
    storageBucket: '${this.config.target.storageBucket}',
    iosBundleId: '${this.config.platform_configs.ios.bundle_id}',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: '${this.config.target.webApiKey || 'YOUR_WEB_API_KEY'}',
    appId: '${this.config.target.webAppId || 'YOUR_WEB_APP_ID'}',
    messagingSenderId: '${this.config.target.projectNumber}',
    projectId: '${this.config.target.projectId}',
    authDomain: '${this.config.target.projectId}.firebaseapp.com',
    storageBucket: '${this.config.target.storageBucket}',
  );
}
`;

    fs.writeFileSync(firebaseOptionsPath, firebaseOptionsContent);
    console.log('   ✅ Manual firebase_options.dart created');
  }

  async updateAndroidConfig() {
    console.log('\n🤖 Updating Android configuration...');
    
    const androidConfigPath = path.join(this.projectRoot, this.config.platform_configs.android.config_file);
    
    if (!fs.existsSync(androidConfigPath)) {
      console.log('   ⚠️  Android config file not found, skipping...');
      return;
    }
    
    // Backup existing file
    fs.copyFileSync(androidConfigPath, androidConfigPath + this.backupSuffix);
    
    // Check if new google-services.json is provided
    const newConfigPath = path.join(__dirname, 'platform-configs', 'google-services.json');
    
    if (fs.existsSync(newConfigPath)) {
      fs.copyFileSync(newConfigPath, androidConfigPath);
      console.log('   ✅ Android google-services.json updated');
    } else {
      console.log('   ⚠️  New google-services.json not found in platform-configs/');
      console.log('   📝 Please manually download and replace:');
      console.log(`      ${androidConfigPath}`);
    }
    
    // Update SHA certificates if needed
    if (this.config.platform_configs.android.update_sha_certificates) {
      await this.updateSHACertificates();
    }
  }

  async updateSHACertificates() {
    console.log('   🔐 Updating SHA certificates...');
    
    try {
      // Get debug keystore SHA-1
      const debugKeystorePath = path.join(process.env.USERPROFILE || process.env.HOME, '.android', 'debug.keystore');
      
      if (fs.existsSync(debugKeystorePath)) {
        const sha1Command = `keytool -list -v -keystore "${debugKeystorePath}" -alias androiddebugkey -storepass android -keypass android`;
        const sha1Output = execSync(sha1Command, { encoding: 'utf8' });
        
        const sha1Match = sha1Output.match(/SHA1: ([A-F0-9:]+)/);
        if (sha1Match) {
          console.log(`   📋 Debug SHA-1: ${sha1Match[1]}`);
          console.log('   📝 Please add this SHA-1 to your Firebase project console');
        }
      }
      
    } catch (error) {
      console.warn('   ⚠️  Could not extract SHA certificates:', error.message);
    }
  }

  async updateIOSConfig() {
    console.log('\n🍎 Updating iOS configuration...');
    
    const iosConfigPath = path.join(this.projectRoot, this.config.platform_configs.ios.config_file);
    
    if (!fs.existsSync(iosConfigPath)) {
      console.log('   ⚠️  iOS config file not found, skipping...');
      return;
    }
    
    // Backup existing file
    fs.copyFileSync(iosConfigPath, iosConfigPath + this.backupSuffix);
    
    // Check if new GoogleService-Info.plist is provided
    const newConfigPath = path.join(__dirname, 'platform-configs', 'GoogleService-Info.plist');
    
    if (fs.existsSync(newConfigPath)) {
      fs.copyFileSync(newConfigPath, iosConfigPath);
      console.log('   ✅ iOS GoogleService-Info.plist updated');
    } else {
      console.log('   ⚠️  New GoogleService-Info.plist not found in platform-configs/');
      console.log('   📝 Please manually download and replace:');
      console.log(`      ${iosConfigPath}`);
    }
  }

  async updateWebConfig() {
    console.log('\n🌐 Updating Web configuration...');
    
    const webConfigPath = path.join(this.projectRoot, this.config.platform_configs.web.config_file);
    
    // Create web config if it doesn't exist
    if (!fs.existsSync(path.dirname(webConfigPath))) {
      fs.mkdirSync(path.dirname(webConfigPath), { recursive: true });
    }
    
    // Backup existing file if it exists
    if (fs.existsSync(webConfigPath)) {
      fs.copyFileSync(webConfigPath, webConfigPath + this.backupSuffix);
    }
    
    // Create new web config
    const webConfigContent = `// Firebase configuration for web
const firebaseConfig = {
  apiKey: "${this.config.target.webApiKey || 'YOUR_WEB_API_KEY'}",
  authDomain: "${this.config.target.projectId}.firebaseapp.com",
  projectId: "${this.config.target.projectId}",
  storageBucket: "${this.config.target.storageBucket}",
  messagingSenderId: "${this.config.target.projectNumber}",
  appId: "${this.config.target.webAppId || 'YOUR_WEB_APP_ID'}"
};

// Initialize Firebase
import { initializeApp } from 'firebase/app';
const app = initializeApp(firebaseConfig);

export default app;
`;

    fs.writeFileSync(webConfigPath, webConfigContent);
    console.log('   ✅ Web firebase-config.js updated');
  }

  async updateFlutterConfigs() {
    console.log('\n📱 Updating Flutter-specific configurations...');
    
    // Update Firebase configuration in lib/config/firebase_config.dart
    await this.updateFirebaseConfigDart();
    
    // Update .firebaserc
    await this.updateFirebaseRC();
    
    // Update firebase.json if needed
    await this.updateFirebaseJSON();
  }

  async updateFirebaseConfigDart() {
    const configPath = path.join(this.projectRoot, 'lib', 'config', 'firebase_config.dart');
    
    if (fs.existsSync(configPath)) {
      // Backup existing file
      fs.copyFileSync(configPath, configPath + this.backupSuffix);
      
      // Read and update the configuration
      let configContent = fs.readFileSync(configPath, 'utf8');
      
      // Update project-specific references (if any)
      configContent = configContent.replace(
        new RegExp(this.config.source.projectId, 'g'),
        this.config.target.projectId
      );
      
      fs.writeFileSync(configPath, configContent);
      console.log('   ✅ firebase_config.dart updated');
    }
  }

  async updateFirebaseRC() {
    const firebaseRCPath = path.join(this.projectRoot, '.firebaserc');
    
    if (fs.existsSync(firebaseRCPath)) {
      // Backup existing file
      fs.copyFileSync(firebaseRCPath, firebaseRCPath + this.backupSuffix);
      
      const firebaseRC = {
        "projects": {
          "default": this.config.target.projectId,
          "doc": this.config.target.projectId
        },
        "targets": {},
        "etags": {}
      };
      
      fs.writeFileSync(firebaseRCPath, JSON.stringify(firebaseRC, null, 2));
      console.log('   ✅ .firebaserc updated');
    }
  }

  async updateFirebaseJSON() {
    const firebaseJSONPath = path.join(this.projectRoot, 'firebase.json');
    
    if (fs.existsSync(firebaseJSONPath)) {
      // Backup existing file
      fs.copyFileSync(firebaseJSONPath, firebaseJSONPath + this.backupSuffix);
      
      // Read existing firebase.json
      const firebaseJSON = JSON.parse(fs.readFileSync(firebaseJSONPath, 'utf8'));
      
      // Update any project-specific references
      if (firebaseJSON.hosting && firebaseJSON.hosting.site) {
        firebaseJSON.hosting.site = this.config.target.projectId;
      }
      
      fs.writeFileSync(firebaseJSONPath, JSON.stringify(firebaseJSON, null, 2));
      console.log('   ✅ firebase.json updated');
    }
  }

  async generateConfigurationReport() {
    const report = {
      migration_id: this.config.migration.id,
      target_project: this.config.target.projectId,
      updated_files: [],
      backup_files: [],
      manual_steps_required: [],
      timestamp: new Date().toISOString()
    };
    
    // List all backup files created
    const findBackupFiles = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          findBackupFiles(fullPath);
        } else if (item.includes(this.backupSuffix)) {
          report.backup_files.push(fullPath);
        }
      }
    };
    
    findBackupFiles(this.projectRoot);
    
    // Add manual steps if needed
    if (!fs.existsSync(path.join(__dirname, 'platform-configs', 'google-services.json'))) {
      report.manual_steps_required.push('Download and replace android/app/google-services.json');
    }
    
    if (!fs.existsSync(path.join(__dirname, 'platform-configs', 'GoogleService-Info.plist'))) {
      report.manual_steps_required.push('Download and replace ios/Runner/GoogleService-Info.plist');
    }
    
    const reportPath = path.join(__dirname, '../logs', `config-update-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Configuration Update Report:');
    console.log(`   Backup files created: ${report.backup_files.length}`);
    console.log(`   Manual steps required: ${report.manual_steps_required.length}`);
    console.log(`   Report saved: ${reportPath}`);
    
    if (report.manual_steps_required.length > 0) {
      console.log('\n📝 Manual Steps Required:');
      report.manual_steps_required.forEach((step, index) => {
        console.log(`   ${index + 1}. ${step}`);
      });
    }
  }
}

// Run configuration update if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found. Run setup first: node config/setup-migration-config.js');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  const updater = new FlutterConfigUpdater(config);
  
  updater.updateAllConfigurations()
    .then(() => updater.generateConfigurationReport())
    .then(() => {
      console.log('\n🎉 Flutter configuration update completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Flutter configuration update failed:', error.message);
      process.exit(1);
    });
}

module.exports = FlutterConfigUpdater;
