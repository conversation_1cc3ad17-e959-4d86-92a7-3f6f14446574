#!/usr/bin/env node

/**
 * Firebase Authentication Migration Script
 * 
 * Migrates Firebase Authentication users from source to target project
 * while preserving user IDs and custom claims.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

class AuthMigration {
  constructor(config) {
    this.config = config;
    this.sourceAuth = null;
    this.targetAuth = null;
    this.migrationStats = {
      users: 0,
      errors: 0,
      startTime: null,
      endTime: null
    };
    this.errors = [];
  }

  async initialize() {
    console.log('🔄 Initializing Auth Migration...');
    
    try {
      // Initialize source Firebase app
      const sourceServiceAccount = require(path.resolve(__dirname, this.config.source.serviceAccountPath));
      const sourceApp = admin.initializeApp({
        credential: admin.credential.cert(sourceServiceAccount),
        projectId: this.config.source.projectId
      }, 'source-auth');
      
      this.sourceAuth = admin.auth(sourceApp);
      
      // Initialize target Firebase app
      const targetServiceAccount = require(path.resolve(__dirname, this.config.target.serviceAccountPath));
      const targetApp = admin.initializeApp({
        credential: admin.credential.cert(targetServiceAccount),
        projectId: this.config.target.projectId
      }, 'target-auth');
      
      this.targetAuth = admin.auth(targetApp);
      
      console.log('✅ Firebase Auth initialized successfully');
      
    } catch (error) {
      throw new Error(`Failed to initialize Firebase Auth: ${error.message}`);
    }
  }

  async migrateAllUsers() {
    console.log('\n🚀 Starting Firebase Auth migration...');
    this.migrationStats.startTime = new Date();
    
    try {
      let nextPageToken;
      let batchCount = 0;
      
      do {
        batchCount++;
        console.log(`\n👥 Processing user batch ${batchCount}...`);
        
        // List users from source project
        const listUsersResult = await this.sourceAuth.listUsers(1000, nextPageToken);
        
        console.log(`   Found ${listUsersResult.users.length} users in batch`);
        
        // Process users in parallel with rate limiting
        const userPromises = listUsersResult.users.map(user => 
          this.migrateUser(user).catch(error => {
            this.errors.push({
              userId: user.uid,
              email: user.email,
              error: error.message,
              timestamp: new Date().toISOString()
            });
            console.error(`❌ Failed to migrate user ${user.email}:`, error.message);
          })
        );
        
        await Promise.allSettled(userPromises);
        
        nextPageToken = listUsersResult.pageToken;
        
        // Add delay to avoid rate limiting
        if (nextPageToken) {
          await this.delay(1000);
        }
        
      } while (nextPageToken);
      
      this.migrationStats.endTime = new Date();
      await this.generateMigrationReport();
      
      console.log('\n✅ Auth migration completed successfully!');
      
    } catch (error) {
      this.migrationStats.endTime = new Date();
      await this.generateMigrationReport();
      throw error;
    }
  }

  async migrateUser(sourceUser) {
    try {
      console.log(`   Migrating user: ${sourceUser.email || sourceUser.uid}`);
      
      // Check if user already exists in target
      let targetUser;
      try {
        targetUser = await this.targetAuth.getUser(sourceUser.uid);
        console.log(`   ⚠️  User ${sourceUser.email} already exists in target project`);
        
        if (this.config.auth_migration.preserve_user_ids) {
          // Update existing user
          await this.updateExistingUser(targetUser, sourceUser);
          return;
        }
      } catch (error) {
        // User doesn't exist, proceed with creation
      }
      
      // Prepare user data for import
      const userImportRecord = this.prepareUserImportRecord(sourceUser);
      
      // Import user to target project
      const importResult = await this.targetAuth.importUsers([userImportRecord]);
      
      if (importResult.errors && importResult.errors.length > 0) {
        throw new Error(`Import failed: ${importResult.errors[0].error.message}`);
      }
      
      // Migrate custom claims if enabled
      if (this.config.auth_migration.migrate_custom_claims && sourceUser.customClaims) {
        await this.targetAuth.setCustomUserClaims(sourceUser.uid, sourceUser.customClaims);
      }
      
      // Send password reset email if enabled
      if (this.config.auth_migration.send_password_reset && sourceUser.email) {
        try {
          await this.targetAuth.generatePasswordResetLink(sourceUser.email);
          console.log(`   📧 Password reset email sent to ${sourceUser.email}`);
        } catch (error) {
          console.warn(`   ⚠️  Failed to send password reset to ${sourceUser.email}:`, error.message);
        }
      }
      
      this.migrationStats.users++;
      console.log(`   ✅ User migrated successfully: ${sourceUser.email || sourceUser.uid}`);
      
    } catch (error) {
      this.migrationStats.errors++;
      throw error;
    }
  }

  prepareUserImportRecord(sourceUser) {
    const userRecord = {
      uid: sourceUser.uid,
      email: sourceUser.email,
      emailVerified: sourceUser.emailVerified,
      displayName: sourceUser.displayName,
      photoURL: sourceUser.photoURL,
      phoneNumber: sourceUser.phoneNumber,
      disabled: sourceUser.disabled,
      metadata: {
        creationTime: sourceUser.metadata.creationTime,
        lastSignInTime: sourceUser.metadata.lastSignInTime
      },
      customClaims: sourceUser.customClaims,
      providerData: sourceUser.providerData
    };
    
    // Handle password hash if available (for email/password users)
    if (sourceUser.passwordHash) {
      userRecord.passwordHash = sourceUser.passwordHash;
      userRecord.passwordSalt = sourceUser.passwordSalt;
    }
    
    return userRecord;
  }

  async updateExistingUser(targetUser, sourceUser) {
    console.log(`   🔄 Updating existing user: ${sourceUser.email}`);
    
    const updateRequest = {};
    
    // Update fields that might have changed
    if (sourceUser.displayName !== targetUser.displayName) {
      updateRequest.displayName = sourceUser.displayName;
    }
    
    if (sourceUser.photoURL !== targetUser.photoURL) {
      updateRequest.photoURL = sourceUser.photoURL;
    }
    
    if (sourceUser.phoneNumber !== targetUser.phoneNumber) {
      updateRequest.phoneNumber = sourceUser.phoneNumber;
    }
    
    if (sourceUser.disabled !== targetUser.disabled) {
      updateRequest.disabled = sourceUser.disabled;
    }
    
    if (Object.keys(updateRequest).length > 0) {
      await this.targetAuth.updateUser(sourceUser.uid, updateRequest);
      console.log(`   ✅ User updated: ${sourceUser.email}`);
    }
    
    // Update custom claims
    if (this.config.auth_migration.migrate_custom_claims && sourceUser.customClaims) {
      await this.targetAuth.setCustomUserClaims(sourceUser.uid, sourceUser.customClaims);
      console.log(`   ✅ Custom claims updated: ${sourceUser.email}`);
    }
  }

  async generateMigrationReport() {
    const duration = this.migrationStats.endTime - this.migrationStats.startTime;
    const report = {
      migration_id: this.config.migration.id,
      source_project: this.config.source.projectId,
      target_project: this.config.target.projectId,
      start_time: this.migrationStats.startTime.toISOString(),
      end_time: this.migrationStats.endTime.toISOString(),
      duration_ms: duration,
      duration_formatted: this.formatDuration(duration),
      statistics: {
        users_migrated: this.migrationStats.users,
        errors_count: this.migrationStats.errors,
        success_rate: this.migrationStats.users > 0 ? 
          ((this.migrationStats.users - this.migrationStats.errors) / this.migrationStats.users * 100).toFixed(2) + '%' : '0%'
      },
      errors: this.errors,
      settings: {
        preserve_user_ids: this.config.auth_migration.preserve_user_ids,
        migrate_custom_claims: this.config.auth_migration.migrate_custom_claims,
        send_password_reset: this.config.auth_migration.send_password_reset
      }
    };
    
    const reportPath = path.join(__dirname, '../logs', `auth-migration-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Auth Migration Report Generated:');
    console.log(`   Users: ${report.statistics.users_migrated}`);
    console.log(`   Errors: ${report.statistics.errors_count}`);
    console.log(`   Success Rate: ${report.statistics.success_rate}`);
    console.log(`   Duration: ${report.duration_formatted}`);
    console.log(`   Report saved: ${reportPath}`);
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run migration if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found. Run setup first: node config/setup-migration-config.js');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  
  if (!config.auth_migration.enabled) {
    console.log('⚠️  Auth migration is disabled in configuration');
    process.exit(0);
  }
  
  const migration = new AuthMigration(config);
  
  migration.initialize()
    .then(() => migration.migrateAllUsers())
    .then(() => {
      console.log('\n🎉 Auth migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Auth migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = AuthMigration;
