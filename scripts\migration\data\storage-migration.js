#!/usr/bin/env node

/**
 * Firebase Storage Migration Script
 * 
 * Migrates all files from source Firebase Storage to target Firebase Storage
 * while preserving folder structure and file metadata.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class StorageMigration {
  constructor(config) {
    this.config = config;
    this.sourceBucket = null;
    this.targetBucket = null;
    this.migrationStats = {
      files: 0,
      totalBytes: 0,
      errors: 0,
      startTime: null,
      endTime: null
    };
    this.errors = [];
  }

  async initialize() {
    console.log('🔄 Initializing Storage Migration...');
    
    try {
      // Initialize source Firebase app
      const sourceServiceAccount = require(path.resolve(__dirname, this.config.source.serviceAccountPath));
      const sourceApp = admin.initializeApp({
        credential: admin.credential.cert(sourceServiceAccount),
        projectId: this.config.source.projectId,
        storageBucket: this.config.source.storageBucket
      }, 'source-storage');
      
      this.sourceBucket = admin.storage(sourceApp).bucket();
      
      // Initialize target Firebase app
      const targetServiceAccount = require(path.resolve(__dirname, this.config.target.serviceAccountPath));
      const targetApp = admin.initializeApp({
        credential: admin.credential.cert(targetServiceAccount),
        projectId: this.config.target.projectId,
        storageBucket: this.config.target.storageBucket
      }, 'target-storage');
      
      this.targetBucket = admin.storage(targetApp).bucket();
      
      console.log('✅ Firebase Storage initialized successfully');
      
    } catch (error) {
      throw new Error(`Failed to initialize Firebase Storage: ${error.message}`);
    }
  }

  async migrateAllFiles() {
    console.log('\n🚀 Starting Firebase Storage migration...');
    this.migrationStats.startTime = new Date();
    
    try {
      const foldersToMigrate = this.config.storage_migration.folders_to_migrate;
      
      for (const folder of foldersToMigrate) {
        await this.migrateFolder(folder);
      }
      
      this.migrationStats.endTime = new Date();
      await this.generateMigrationReport();
      
      console.log('\n✅ Storage migration completed successfully!');
      
    } catch (error) {
      this.migrationStats.endTime = new Date();
      await this.generateMigrationReport();
      throw error;
    }
  }

  async migrateFolder(folderPath) {
    console.log(`\n📁 Migrating folder: ${folderPath}`);
    
    try {
      // Get all files in the folder
      const [files] = await this.sourceBucket.getFiles({
        prefix: folderPath,
        delimiter: this.config.storage_migration.preserve_folder_structure ? undefined : '/'
      });
      
      console.log(`   Found ${files.length} files in ${folderPath}`);
      
      if (files.length === 0) {
        console.log(`   ⚠️  No files found in ${folderPath}`);
        return;
      }
      
      // Process files in batches to avoid memory issues
      const batchSize = 10; // Process 10 files at a time
      
      for (let i = 0; i < files.length; i += batchSize) {
        const batch = files.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(files.length / batchSize);
        
        console.log(`   Processing batch ${batchNumber}/${totalBatches} (${batch.length} files)...`);
        
        // Process batch in parallel
        const promises = batch.map(file => this.migrateFile(file));
        await Promise.allSettled(promises);
        
        // Add delay between batches
        if (i + batchSize < files.length) {
          await this.delay(500);
        }
      }
      
      console.log(`✅ Folder ${folderPath} migration completed`);
      
    } catch (error) {
      console.error(`❌ Failed to migrate folder ${folderPath}:`, error.message);
      this.errors.push({
        folder: folderPath,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async migrateFile(sourceFile) {
    try {
      const fileName = sourceFile.name;
      console.log(`     Migrating file: ${fileName}`);
      
      // Check if file already exists in target
      const targetFile = this.targetBucket.file(fileName);
      const [targetExists] = await targetFile.exists();
      
      if (targetExists) {
        console.log(`     ⚠️  File already exists: ${fileName}`);
        
        if (this.config.storage_migration.verify_file_integrity) {
          const isIdentical = await this.verifyFileIntegrity(sourceFile, targetFile);
          if (isIdentical) {
            console.log(`     ✅ File verified identical: ${fileName}`);
            return;
          } else {
            console.log(`     🔄 File differs, re-uploading: ${fileName}`);
          }
        } else {
          return; // Skip if not verifying integrity
        }
      }
      
      // Get source file metadata
      const [sourceMetadata] = await sourceFile.getMetadata();
      
      // Download file to memory (for small files) or temp file (for large files)
      const fileSize = parseInt(sourceMetadata.size);
      const maxMemorySize = 50 * 1024 * 1024; // 50MB
      
      let fileData;
      let tempFilePath;
      
      if (fileSize <= maxMemorySize) {
        // Download to memory for small files
        const [buffer] = await sourceFile.download();
        fileData = buffer;
      } else {
        // Download to temp file for large files
        tempFilePath = path.join(__dirname, '../temp', `temp-${Date.now()}-${path.basename(fileName)}`);
        await this.ensureDirectoryExists(path.dirname(tempFilePath));
        await sourceFile.download({ destination: tempFilePath });
      }
      
      // Prepare upload options
      const uploadOptions = {
        metadata: {
          contentType: sourceMetadata.contentType,
          metadata: {
            ...sourceMetadata.metadata,
            migratedFrom: this.config.source.projectId,
            migrationId: this.config.migration.id,
            originalUploadTime: sourceMetadata.timeCreated
          }
        }
      };
      
      // Upload to target bucket
      if (fileData) {
        // Upload from memory
        await targetFile.save(fileData, uploadOptions);
      } else {
        // Upload from temp file
        await this.targetBucket.upload(tempFilePath, {
          destination: fileName,
          ...uploadOptions
        });
        
        // Clean up temp file
        fs.unlinkSync(tempFilePath);
      }
      
      // Verify upload if enabled
      if (this.config.storage_migration.verify_file_integrity) {
        const uploadSuccess = await this.verifyFileIntegrity(sourceFile, targetFile);
        if (!uploadSuccess) {
          throw new Error('File integrity verification failed after upload');
        }
      }
      
      this.migrationStats.files++;
      this.migrationStats.totalBytes += fileSize;
      
      console.log(`     ✅ File migrated: ${fileName} (${this.formatBytes(fileSize)})`);
      
    } catch (error) {
      this.migrationStats.errors++;
      this.errors.push({
        file: sourceFile.name,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      console.error(`     ❌ Failed to migrate file ${sourceFile.name}:`, error.message);
    }
  }

  async verifyFileIntegrity(sourceFile, targetFile) {
    try {
      const [sourceMetadata] = await sourceFile.getMetadata();
      const [targetMetadata] = await targetFile.getMetadata();
      
      // Compare file sizes
      if (sourceMetadata.size !== targetMetadata.size) {
        return false;
      }
      
      // Compare MD5 hashes if available
      if (sourceMetadata.md5Hash && targetMetadata.md5Hash) {
        return sourceMetadata.md5Hash === targetMetadata.md5Hash;
      }
      
      // If no MD5 hash, compare file contents for small files
      const fileSize = parseInt(sourceMetadata.size);
      if (fileSize <= 10 * 1024 * 1024) { // 10MB limit for content comparison
        const [sourceBuffer] = await sourceFile.download();
        const [targetBuffer] = await targetFile.download();
        
        const sourceHash = crypto.createHash('md5').update(sourceBuffer).digest('hex');
        const targetHash = crypto.createHash('md5').update(targetBuffer).digest('hex');
        
        return sourceHash === targetHash;
      }
      
      // For large files without MD5, assume identical if sizes match
      return true;
      
    } catch (error) {
      console.warn(`     ⚠️  Could not verify file integrity: ${error.message}`);
      return true; // Assume success if verification fails
    }
  }

  async ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  async generateMigrationReport() {
    const duration = this.migrationStats.endTime - this.migrationStats.startTime;
    const report = {
      migration_id: this.config.migration.id,
      source_project: this.config.source.projectId,
      target_project: this.config.target.projectId,
      start_time: this.migrationStats.startTime.toISOString(),
      end_time: this.migrationStats.endTime.toISOString(),
      duration_ms: duration,
      duration_formatted: this.formatDuration(duration),
      statistics: {
        files_migrated: this.migrationStats.files,
        total_bytes: this.migrationStats.totalBytes,
        total_size_formatted: this.formatBytes(this.migrationStats.totalBytes),
        errors_count: this.migrationStats.errors,
        success_rate: this.migrationStats.files > 0 ? 
          ((this.migrationStats.files - this.migrationStats.errors) / this.migrationStats.files * 100).toFixed(2) + '%' : '0%'
      },
      errors: this.errors,
      settings: {
        preserve_folder_structure: this.config.storage_migration.preserve_folder_structure,
        verify_file_integrity: this.config.storage_migration.verify_file_integrity,
        folders_migrated: this.config.storage_migration.folders_to_migrate
      }
    };
    
    const reportPath = path.join(__dirname, '../logs', `storage-migration-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Storage Migration Report Generated:');
    console.log(`   Files: ${report.statistics.files_migrated}`);
    console.log(`   Total Size: ${report.statistics.total_size_formatted}`);
    console.log(`   Errors: ${report.statistics.errors_count}`);
    console.log(`   Success Rate: ${report.statistics.success_rate}`);
    console.log(`   Duration: ${report.duration_formatted}`);
    console.log(`   Report saved: ${reportPath}`);
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run migration if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found. Run setup first: node config/setup-migration-config.js');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  
  if (!config.storage_migration.enabled) {
    console.log('⚠️  Storage migration is disabled in configuration');
    process.exit(0);
  }
  
  const migration = new StorageMigration(config);
  
  migration.initialize()
    .then(() => migration.migrateAllFiles())
    .then(() => {
      console.log('\n🎉 Storage migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Storage migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = StorageMigration;
