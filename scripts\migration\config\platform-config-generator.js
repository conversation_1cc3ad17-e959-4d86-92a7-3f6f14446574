#!/usr/bin/env node

/**
 * Platform Configuration Generator
 * 
 * Generates platform-specific configuration files for Android, iOS, and Web
 * based on the new Firebase project settings.
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

class PlatformConfigGenerator {
  constructor(config) {
    this.config = config;
    this.platformConfigsPath = path.join(__dirname, 'platform-configs');
  }

  async generateAllConfigurations() {
    console.log('🔄 Starting platform configuration generation...');
    
    try {
      // Create platform-configs directory
      if (!fs.existsSync(this.platformConfigsPath)) {
        fs.mkdirSync(this.platformConfigsPath, { recursive: true });
      }
      
      // Generate Android configuration
      await this.generateAndroidConfig();
      
      // Generate iOS configuration
      await this.generateIOSConfig();
      
      // Generate Web configuration
      await this.generateWebConfig();
      
      // Generate Windows configuration
      await this.generateWindowsConfig();
      
      console.log('\n✅ Platform configuration generation completed!');
      
    } catch (error) {
      console.error('\n❌ Platform configuration generation failed:', error.message);
      throw error;
    }
  }

  async generateAndroidConfig() {
    console.log('\n🤖 Generating Android configuration...');
    
    try {
      // Generate google-services.json template
      const androidConfig = {
        "project_info": {
          "project_number": this.config.target.projectNumber,
          "project_id": this.config.target.projectId,
          "storage_bucket": this.config.target.storageBucket
        },
        "client": [
          {
            "client_info": {
              "mobilesdk_app_id": this.config.target.androidAppId || "1:{{PROJECT_NUMBER}}:android:{{APP_ID}}",
              "android_client_info": {
                "package_name": this.config.platform_configs.android.package_name
              }
            },
            "oauth_client": [
              {
                "client_id": "{{OAUTH_CLIENT_ID}}",
                "client_type": 1,
                "android_info": {
                  "package_name": this.config.platform_configs.android.package_name,
                  "certificate_hash": "{{SHA1_CERTIFICATE_HASH}}"
                }
              }
            ],
            "api_key": [
              {
                "current_key": this.config.target.androidApiKey || "{{ANDROID_API_KEY}}"
              }
            ],
            "services": {
              "appinvite_service": {
                "other_platform_oauth_client": []
              }
            }
          }
        ],
        "configuration_version": "1"
      };
      
      const androidConfigPath = path.join(this.platformConfigsPath, 'google-services.json');
      fs.writeFileSync(androidConfigPath, JSON.stringify(androidConfig, null, 2));
      
      console.log('   ✅ google-services.json template generated');
      console.log('   📝 Please replace placeholders with actual values from Firebase Console');
      
    } catch (error) {
      throw new Error(`Failed to generate Android config: ${error.message}`);
    }
  }

  async generateIOSConfig() {
    console.log('\n🍎 Generating iOS configuration...');
    
    try {
      // Generate GoogleService-Info.plist template
      const iosConfigContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>${this.config.target.iosClientId || '{{IOS_CLIENT_ID}}'}</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>${this.config.target.iosReversedClientId || '{{IOS_REVERSED_CLIENT_ID}}'}</string>
	<key>API_KEY</key>
	<string>${this.config.target.iosApiKey || '{{IOS_API_KEY}}'}</string>
	<key>GCM_SENDER_ID</key>
	<string>${this.config.target.projectNumber}</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>${this.config.platform_configs.ios.bundle_id}</string>
	<key>PROJECT_ID</key>
	<string>${this.config.target.projectId}</string>
	<key>STORAGE_BUCKET</key>
	<string>${this.config.target.storageBucket}</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>${this.config.target.iosAppId || '{{IOS_APP_ID}}'}</string>
</dict>
</plist>`;
      
      const iosConfigPath = path.join(this.platformConfigsPath, 'GoogleService-Info.plist');
      fs.writeFileSync(iosConfigPath, iosConfigContent);
      
      console.log('   ✅ GoogleService-Info.plist template generated');
      console.log('   📝 Please replace placeholders with actual values from Firebase Console');
      
    } catch (error) {
      throw new Error(`Failed to generate iOS config: ${error.message}`);
    }
  }

  async generateWebConfig() {
    console.log('\n🌐 Generating Web configuration...');
    
    try {
      // Generate web configuration
      const webConfigContent = `// Firebase configuration for web
const firebaseConfig = {
  apiKey: "${this.config.target.webApiKey || '{{WEB_API_KEY}}'}",
  authDomain: "${this.config.target.projectId}.firebaseapp.com",
  projectId: "${this.config.target.projectId}",
  storageBucket: "${this.config.target.storageBucket}",
  messagingSenderId: "${this.config.target.projectNumber}",
  appId: "${this.config.target.webAppId || '{{WEB_APP_ID}}'}",
  measurementId: "${this.config.target.measurementId || '{{MEASUREMENT_ID}}'}"
};

// Initialize Firebase
import { initializeApp } from 'firebase/app';
import { getAnalytics } from 'firebase/analytics';

const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);

export { app, analytics };
export default firebaseConfig;
`;
      
      const webConfigPath = path.join(this.platformConfigsPath, 'firebase-config.js');
      fs.writeFileSync(webConfigPath, webConfigContent);
      
      // Generate HTML script version
      const webConfigHTMLContent = `<!-- Firebase configuration for web (HTML script version) -->
<script type="module">
  // Import the functions you need from the SDKs you need
  import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
  import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

  // Your web app's Firebase configuration
  const firebaseConfig = {
    apiKey: "${this.config.target.webApiKey || '{{WEB_API_KEY}}'}",
    authDomain: "${this.config.target.projectId}.firebaseapp.com",
    projectId: "${this.config.target.projectId}",
    storageBucket: "${this.config.target.storageBucket}",
    messagingSenderId: "${this.config.target.projectNumber}",
    appId: "${this.config.target.webAppId || '{{WEB_APP_ID}}'}",
    measurementId: "${this.config.target.measurementId || '{{MEASUREMENT_ID}}'}"
  };

  // Initialize Firebase
  const app = initializeApp(firebaseConfig);
  const analytics = getAnalytics(app);
</script>`;
      
      const webConfigHTMLPath = path.join(this.platformConfigsPath, 'firebase-config.html');
      fs.writeFileSync(webConfigHTMLPath, webConfigHTMLContent);
      
      console.log('   ✅ Web configuration files generated');
      console.log('   📝 Please replace placeholders with actual values from Firebase Console');
      
    } catch (error) {
      throw new Error(`Failed to generate Web config: ${error.message}`);
    }
  }

  async generateWindowsConfig() {
    console.log('\n🪟 Generating Windows configuration...');
    
    try {
      // Generate Windows configuration (similar to web)
      const windowsConfigContent = `// Firebase configuration for Windows
using System;
using System.Collections.Generic;

namespace DocumentManagement.Config
{
    public static class FirebaseConfig
    {
        public static readonly Dictionary<string, string> Config = new Dictionary<string, string>
        {
            {"apiKey", "${this.config.target.webApiKey || '{{WEB_API_KEY}}'}"},
            {"authDomain", "${this.config.target.projectId}.firebaseapp.com"},
            {"projectId", "${this.config.target.projectId}"},
            {"storageBucket", "${this.config.target.storageBucket}"},
            {"messagingSenderId", "${this.config.target.projectNumber}"},
            {"appId", "${this.config.target.webAppId || '{{WEB_APP_ID}}'}"}
        };
        
        public const string ProjectId = "${this.config.target.projectId}";
        public const string StorageBucket = "${this.config.target.storageBucket}";
        public const string ApiKey = "${this.config.target.webApiKey || '{{WEB_API_KEY}}'}";
    }
}`;
      
      const windowsConfigPath = path.join(this.platformConfigsPath, 'FirebaseConfig.cs');
      fs.writeFileSync(windowsConfigPath, windowsConfigContent);
      
      console.log('   ✅ Windows configuration generated');
      
    } catch (error) {
      throw new Error(`Failed to generate Windows config: ${error.message}`);
    }
  }

  async generateConfigurationInstructions() {
    console.log('\n📋 Generating configuration instructions...');
    
    const instructionsContent = `# Firebase Project Configuration Instructions

## Overview
This document provides step-by-step instructions for configuring your new Firebase project with the generated configuration files.

## 1. Firebase Console Setup

### Step 1: Create Firebase Apps
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project: **${this.config.target.projectId}**
3. Click on "Project Settings" (gear icon)
4. Go to "General" tab

### Step 2: Add Android App
1. Click "Add app" and select Android
2. Package name: \`${this.config.platform_configs.android.package_name}\`
3. App nickname: \`SIMDOC Android\`
4. Download the \`google-services.json\` file
5. Replace the generated template with the downloaded file

### Step 3: Add iOS App (if needed)
1. Click "Add app" and select iOS
2. Bundle ID: \`${this.config.platform_configs.ios.bundle_id}\`
3. App nickname: \`SIMDOC iOS\`
4. Download the \`GoogleService-Info.plist\` file
5. Replace the generated template with the downloaded file

### Step 4: Add Web App
1. Click "Add app" and select Web
2. App nickname: \`SIMDOC Web\`
3. Copy the configuration object
4. Update the web configuration files with actual values

## 2. Configuration File Updates

### Android Configuration
- File: \`android/app/google-services.json\`
- Source: Download from Firebase Console > Project Settings > General > Your apps > Android app
- Action: Replace the template file with the downloaded version

### iOS Configuration
- File: \`ios/Runner/GoogleService-Info.plist\`
- Source: Download from Firebase Console > Project Settings > General > Your apps > iOS app
- Action: Replace the template file with the downloaded version

### Web Configuration
- File: \`web/firebase-config.js\`
- Source: Firebase Console > Project Settings > General > Your apps > Web app > Config
- Action: Replace placeholder values with actual configuration

### Flutter Configuration
- File: \`lib/firebase_options.dart\`
- Source: Generated automatically by FlutterFire CLI or manually updated
- Action: Run \`flutterfire configure\` or update manually

## 3. Security Configuration

### SHA-1 Certificate (Android)
1. Generate debug SHA-1:
   \`\`\`bash
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   \`\`\`
2. Add the SHA-1 to Firebase Console > Project Settings > General > Your apps > Android app

### Bundle ID Verification (iOS)
1. Verify Bundle ID matches: \`${this.config.platform_configs.ios.bundle_id}\`
2. Update in Xcode if necessary

## 4. Firebase Services Setup

### Authentication
1. Go to Firebase Console > Authentication
2. Enable sign-in methods:
   - Email/Password
   - Google (optional)
3. Configure authorized domains

### Firestore Database
1. Go to Firebase Console > Firestore Database
2. Create database in production mode
3. Deploy security rules: \`firebase deploy --only firestore:rules\`
4. Deploy indexes: \`firebase deploy --only firestore:indexes\`

### Storage
1. Go to Firebase Console > Storage
2. Create default bucket
3. Deploy storage rules: \`firebase deploy --only storage\`

### Functions
1. Ensure billing is enabled
2. Deploy functions: \`firebase deploy --only functions\`

## 5. Verification Steps

### Test Configuration
1. Run Flutter app: \`flutter run\`
2. Test authentication
3. Test Firestore operations
4. Test file upload/download
5. Verify functions are working

### Common Issues
- **Build errors**: Check package names and bundle IDs
- **Auth errors**: Verify SHA-1 certificates and authorized domains
- **Storage errors**: Check storage rules and bucket configuration
- **Functions errors**: Verify billing and deployment

## 6. Environment Variables

Update the following files with actual values:

### .env (if used)
\`\`\`
FIREBASE_PROJECT_ID=${this.config.target.projectId}
FIREBASE_STORAGE_BUCKET=${this.config.target.storageBucket}
FIREBASE_WEB_API_KEY={{WEB_API_KEY}}
FIREBASE_ANDROID_API_KEY={{ANDROID_API_KEY}}
FIREBASE_IOS_API_KEY={{IOS_API_KEY}}
\`\`\`

## 7. Final Checklist

- [ ] Android app configured with correct google-services.json
- [ ] iOS app configured with correct GoogleService-Info.plist
- [ ] Web app configured with correct Firebase config
- [ ] SHA-1 certificates added to Firebase Console
- [ ] Authentication methods enabled
- [ ] Firestore rules and indexes deployed
- [ ] Storage rules deployed
- [ ] Functions deployed and working
- [ ] App builds and runs successfully
- [ ] All Firebase services tested

## Support

If you encounter issues:
1. Check Firebase Console for error messages
2. Verify all configuration files are correctly placed
3. Ensure all placeholder values are replaced with actual values
4. Check Flutter and Firebase CLI versions are up to date
`;

    const instructionsPath = path.join(this.platformConfigsPath, 'CONFIGURATION_INSTRUCTIONS.md');
    fs.writeFileSync(instructionsPath, instructionsContent);
    
    console.log('   ✅ Configuration instructions generated');
    console.log(`   📄 Instructions saved: ${instructionsPath}`);
  }

  async generateConfigurationReport() {
    const report = {
      migration_id: this.config.migration.id,
      target_project: this.config.target.projectId,
      generated_files: [],
      placeholder_values: [],
      manual_steps: [],
      timestamp: new Date().toISOString()
    };
    
    // List generated files
    if (fs.existsSync(this.platformConfigsPath)) {
      const files = fs.readdirSync(this.platformConfigsPath);
      report.generated_files = files.map(file => path.join(this.platformConfigsPath, file));
    }
    
    // List placeholder values that need to be replaced
    report.placeholder_values = [
      '{{ANDROID_API_KEY}}',
      '{{IOS_API_KEY}}',
      '{{WEB_API_KEY}}',
      '{{WEB_APP_ID}}',
      '{{IOS_APP_ID}}',
      '{{ANDROID_APP_ID}}',
      '{{SHA1_CERTIFICATE_HASH}}',
      '{{OAUTH_CLIENT_ID}}',
      '{{MEASUREMENT_ID}}'
    ];
    
    // List manual steps
    report.manual_steps = [
      'Download actual google-services.json from Firebase Console',
      'Download actual GoogleService-Info.plist from Firebase Console',
      'Replace placeholder values in configuration files',
      'Add SHA-1 certificates to Firebase Console',
      'Enable Authentication methods in Firebase Console',
      'Deploy Firestore rules and indexes',
      'Deploy Storage rules',
      'Deploy Firebase Functions'
    ];
    
    const reportPath = path.join(__dirname, '../logs', `platform-config-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Platform Configuration Report:');
    console.log(`   Generated files: ${report.generated_files.length}`);
    console.log(`   Placeholder values: ${report.placeholder_values.length}`);
    console.log(`   Manual steps: ${report.manual_steps.length}`);
    console.log(`   Report saved: ${reportPath}`);
  }
}

// Run platform config generation if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found. Run setup first: node config/setup-migration-config.js');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  const generator = new PlatformConfigGenerator(config);
  
  generator.generateAllConfigurations()
    .then(() => generator.generateConfigurationInstructions())
    .then(() => generator.generateConfigurationReport())
    .then(() => {
      console.log('\n🎉 Platform configuration generation completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Platform configuration generation failed:', error.message);
      process.exit(1);
    });
}

module.exports = PlatformConfigGenerator;
