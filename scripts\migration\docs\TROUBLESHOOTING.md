# 🔧 Firebase Migration Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered during Firebase project migration. Issues are organized by migration phase and component for quick reference.

## 🚨 Emergency Procedures

### Immediate Actions for Critical Failures

#### Complete Migration Failure
1. **Stop all migration processes immediately**
2. **Document the error state** (screenshots, logs, error messages)
3. **Execute emergency rollback** if data corruption is suspected
4. **Contact technical lead** before proceeding

#### Data Corruption Detected
1. **STOP**: Do not continue migration
2. **Isolate**: Prevent further writes to target project
3. **Assess**: Determine scope of corruption
4. **Rollback**: Execute rollback procedure immediately

#### Service Outage During Migration
1. **Pause**: Wait for service restoration
2. **Verify**: Check data integrity after service restoration
3. **Resume**: Continue from last successful checkpoint
4. **Monitor**: Increase monitoring during resumed migration

## 📋 Pre-Migration Issues

### Configuration Problems

#### Error: "Migration config not found"
**Cause**: Configuration setup not completed
**Solution**:
```bash
cd scripts/migration
node config/setup-migration-config.js
```

#### Error: "Firebase CLI not installed"
**Cause**: Firebase CLI missing or outdated
**Solution**:
```bash
npm install -g firebase-tools
firebase --version  # Verify installation
firebase login      # Authenticate
```

#### Error: "Cannot access source/target project"
**Cause**: Authentication or permission issues
**Solution**:
1. Verify Firebase authentication: `firebase login`
2. Check project access: `firebase projects:list`
3. Verify service account keys are correct
4. Ensure proper IAM permissions

#### Error: "Service account key not found"
**Cause**: Missing or incorrectly placed service account files
**Solution**:
1. Download service account keys from Firebase Console
2. Place source key at: `scripts/config/service-account-key.json`
3. Place target key at: `scripts/migration/config/target-service-account-key.json`
4. Verify file permissions and accessibility

### System Requirements

#### Error: "Node.js version too old"
**Cause**: Node.js version below 18
**Solution**:
```bash
# Install Node.js 18 or higher
nvm install 18
nvm use 18
node --version  # Verify version
```

#### Error: "Insufficient disk space"
**Cause**: Not enough space for backup and temporary files
**Solution**:
1. Free up disk space (minimum 10GB recommended)
2. Use external storage for backups
3. Clean up temporary files: `npm cache clean --force`

#### Error: "Memory allocation failed"
**Cause**: Insufficient RAM for large data migration
**Solution**:
1. Increase Node.js memory limit: `export NODE_OPTIONS="--max-old-space-size=8192"`
2. Reduce batch sizes in configuration
3. Process collections individually

## 🔄 Migration Phase Issues

### Firestore Migration Problems

#### Error: "Document count mismatch"
**Cause**: Incomplete data transfer or rate limiting
**Solution**:
1. Check Firestore quotas in target project
2. Reduce batch size in configuration
3. Increase retry delays
4. Re-run Firestore migration: `node data/firestore-migration.js`

#### Error: "Permission denied on collection"
**Cause**: Insufficient permissions or security rules
**Solution**:
1. Verify service account has Firestore Admin role
2. Check security rules allow admin access
3. Temporarily disable security rules during migration
4. Re-enable rules after migration completion

#### Error: "Write limit exceeded"
**Cause**: Firestore write rate limits
**Solution**:
1. Reduce batch size from 500 to 100-200
2. Increase delay between batches
3. Implement exponential backoff
4. Monitor Firestore usage in console

#### Error: "Invalid document data"
**Cause**: Data validation failures or type mismatches
**Solution**:
1. Review special handling configuration
2. Check for invalid field names or values
3. Validate timestamp formats
4. Handle null/undefined values properly

### Authentication Migration Problems

#### Error: "User import failed"
**Cause**: Authentication service limits or invalid user data
**Solution**:
1. Check user import quotas (1000 users/minute)
2. Validate user data format
3. Remove invalid users from import
4. Use smaller batch sizes for user import

#### Error: "Custom claims not migrated"
**Cause**: Custom claims migration disabled or permission issues
**Solution**:
1. Enable custom claims migration in configuration
2. Verify service account has proper permissions
3. Check custom claims format and size limits
4. Re-run auth migration with custom claims enabled

#### Error: "Password reset emails failed"
**Cause**: Email service configuration or rate limits
**Solution**:
1. Verify email templates are configured
2. Check email service quotas
3. Disable password reset emails if not critical
4. Send reset emails manually after migration

### Storage Migration Problems

#### Error: "File upload failed"
**Cause**: Storage permissions, size limits, or network issues
**Solution**:
1. Verify storage bucket permissions
2. Check file size limits (5TB max per file)
3. Verify network connectivity
4. Retry failed uploads individually

#### Error: "File integrity verification failed"
**Cause**: Corrupted files or incomplete transfers
**Solution**:
1. Re-upload failed files
2. Compare MD5 hashes manually
3. Check source file accessibility
4. Verify target bucket configuration

#### Error: "Storage quota exceeded"
**Cause**: Target project storage limits
**Solution**:
1. Check storage quotas in Firebase Console
2. Upgrade target project plan if needed
3. Clean up unnecessary files
4. Use multiple storage buckets if needed

### Functions Migration Problems

#### Error: "Functions deployment failed"
**Cause**: Billing, dependencies, or code issues
**Solution**:
1. Verify billing is enabled on target project
2. Check function dependencies: `cd functions && npm install`
3. Build functions: `npm run build`
4. Review function code for project references

#### Error: "Function timeout during deployment"
**Cause**: Large function size or slow network
**Solution**:
1. Optimize function code and dependencies
2. Remove unused dependencies
3. Use faster internet connection
4. Deploy functions individually

#### Error: "Environment variables not set"
**Cause**: Missing configuration in target project
**Solution**:
1. Set environment variables: `firebase functions:config:set`
2. Update function code with new project references
3. Redeploy functions after configuration update

## 🔍 Post-Migration Issues

### Data Integrity Problems

#### Error: "Data integrity check failed"
**Cause**: Incomplete migration or data corruption
**Solution**:
1. Review integrity check report
2. Identify specific failed checks
3. Re-migrate affected collections
4. Verify data manually for critical records

#### Error: "Broken relationships detected"
**Cause**: Missing referenced documents or users
**Solution**:
1. Check user and category collections first
2. Verify foreign key relationships
3. Re-migrate dependent collections
4. Update references manually if needed

#### Error: "Performance degradation"
**Cause**: Inefficient queries or missing indexes
**Solution**:
1. Deploy Firestore indexes: `firebase deploy --only firestore:indexes`
2. Review query performance in console
3. Optimize security rules
4. Monitor resource usage

### Application Configuration Issues

#### Error: "App build fails after migration"
**Cause**: Incorrect configuration files
**Solution**:
1. Download correct config files from Firebase Console
2. Replace template files with actual configurations
3. Update SHA-1 certificates for Android
4. Verify bundle IDs for iOS

#### Error: "Authentication not working in app"
**Cause**: Configuration mismatch or domain issues
**Solution**:
1. Verify authorized domains in Firebase Console
2. Check API keys in configuration files
3. Update OAuth client configurations
4. Test authentication flow step by step

#### Error: "File uploads failing in app"
**Cause**: Storage rules or bucket configuration
**Solution**:
1. Verify storage rules are deployed
2. Check bucket permissions
3. Update storage references in app code
4. Test file upload with admin credentials

## 🔄 Recovery Procedures

### Partial Migration Recovery

#### Scenario: Some collections failed to migrate
**Steps**:
1. Identify failed collections from migration report
2. Fix underlying issues (permissions, quotas, etc.)
3. Re-run migration for specific collections:
   ```bash
   # Edit config to include only failed collections
   node data/firestore-migration.js
   ```
4. Verify data integrity after recovery

#### Scenario: Authentication migration incomplete
**Steps**:
1. Check user count in both projects
2. Identify missing users from logs
3. Re-run auth migration:
   ```bash
   node data/auth-migration.js
   ```
4. Verify user data and custom claims

#### Scenario: Storage files partially migrated
**Steps**:
1. Compare file lists between projects
2. Identify missing files from migration report
3. Re-run storage migration:
   ```bash
   node data/storage-migration.js
   ```
4. Verify file integrity and accessibility

### Complete Rollback Procedure

#### When to Execute Rollback
- Critical data corruption detected
- Migration cannot be completed within maintenance window
- Unrecoverable errors in target project
- User acceptance testing fails

#### Rollback Steps
1. **Stop all migration processes**
2. **Execute rollback script**:
   ```bash
   node orchestration/rollback-system.js /path/to/backup
   ```
3. **Restore configuration files**:
   ```bash
   git checkout -- .firebaserc firebase.json
   git checkout -- lib/firebase_options.dart
   git checkout -- android/app/google-services.json
   ```
4. **Verify rollback completion**
5. **Test application functionality**
6. **Notify stakeholders of rollback**

### Data Recovery from Backup

#### Restore Firestore Data
```bash
# Restore specific collection
node data/backup-system.js --restore --collection=users --backup=/path/to/backup

# Restore all collections
node data/backup-system.js --restore --all --backup=/path/to/backup
```

#### Restore Authentication Users
```bash
# Restore users from backup
node data/auth-migration.js --restore --backup=/path/to/backup
```

## 📊 Monitoring and Diagnostics

### Log Analysis

#### Key Log Files
- `logs/migration-final-report-*.json`: Overall migration status
- `logs/firestore-migration-*.json`: Firestore migration details
- `logs/auth-migration-*.json`: Authentication migration details
- `logs/storage-migration-*.json`: Storage migration details
- `logs/pre-migration-checks-*.json`: Pre-migration validation
- `logs/post-migration-verification-*.json`: Post-migration verification

#### Log Analysis Commands
```bash
# Find errors in logs
grep -r "error\|failed\|ERROR" logs/

# Check migration progress
tail -f logs/migration-final-report-*.json

# Analyze performance issues
grep -r "timeout\|slow\|performance" logs/
```

### Performance Monitoring

#### Firestore Performance
- Monitor read/write operations in Firebase Console
- Check query performance and index usage
- Review security rules evaluation time

#### Storage Performance
- Monitor upload/download speeds
- Check file access patterns
- Review storage usage and quotas

#### Functions Performance
- Monitor function execution time
- Check memory usage and timeouts
- Review function logs for errors

## 📞 Escalation Procedures

### When to Escalate
- Migration blocked for more than 2 hours
- Data corruption detected
- Critical security issues identified
- Rollback procedure fails

### Escalation Contacts
1. **Technical Lead**: [Contact information]
2. **Firebase Support**: [Support channels]
3. **Project Manager**: [PM contact]
4. **Emergency Contact**: [24/7 contact]

### Information to Provide
- Migration ID and timestamp
- Error messages and logs
- Steps taken to resolve
- Current system state
- Impact assessment

---

**Remember**: When in doubt, stop the migration and seek help. It's better to delay than to risk data loss or corruption.
