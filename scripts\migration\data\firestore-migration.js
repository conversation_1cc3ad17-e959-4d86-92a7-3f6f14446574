#!/usr/bin/env node

/**
 * Firestore Data Migration Script
 * 
 * Migrates all Firestore collections from source to target Firebase project
 * with comprehensive error handling, progress tracking, and data validation.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

class FirestoreMigration {
  constructor(config) {
    this.config = config;
    this.sourceDb = null;
    this.targetDb = null;
    this.migrationStats = {
      collections: 0,
      documents: 0,
      errors: 0,
      startTime: null,
      endTime: null
    };
    this.errors = [];
  }

  async initialize() {
    console.log('🔄 Initializing Firestore Migration...');
    
    try {
      // Initialize source Firebase app
      const sourceServiceAccount = require(path.resolve(__dirname, this.config.source.serviceAccountPath));
      const sourceApp = admin.initializeApp({
        credential: admin.credential.cert(sourceServiceAccount),
        projectId: this.config.source.projectId
      }, 'source');
      
      this.sourceDb = admin.firestore(sourceApp);
      
      // Initialize target Firebase app
      const targetServiceAccount = require(path.resolve(__dirname, this.config.target.serviceAccountPath));
      const targetApp = admin.initializeApp({
        credential: admin.credential.cert(targetServiceAccount),
        projectId: this.config.target.projectId
      }, 'target');
      
      this.targetDb = admin.firestore(targetApp);
      
      console.log('✅ Firebase apps initialized successfully');
      
    } catch (error) {
      throw new Error(`Failed to initialize Firebase apps: ${error.message}`);
    }
  }

  async migrateAllCollections() {
    console.log('\n🚀 Starting Firestore data migration...');
    this.migrationStats.startTime = new Date();
    
    try {
      // Sort collections by priority
      const sortedCollections = this.config.collections_to_migrate
        .sort((a, b) => a.priority - b.priority);
      
      for (const collectionConfig of sortedCollections) {
        await this.migrateCollection(collectionConfig);
      }
      
      this.migrationStats.endTime = new Date();
      await this.generateMigrationReport();
      
      console.log('\n✅ Firestore migration completed successfully!');
      
    } catch (error) {
      this.migrationStats.endTime = new Date();
      await this.generateMigrationReport();
      throw error;
    }
  }

  async migrateCollection(collectionConfig) {
    const { name, special_handling = [] } = collectionConfig;
    
    console.log(`\n📁 Migrating collection: ${name}`);
    console.log(`   Priority: ${collectionConfig.priority}`);
    console.log(`   Estimated documents: ${collectionConfig.estimated_documents}`);
    
    try {
      const sourceCollection = this.sourceDb.collection(name);
      const targetCollection = this.targetDb.collection(name);
      
      // Get all documents in batches
      let lastDoc = null;
      let batchCount = 0;
      let documentCount = 0;
      
      while (true) {
        let query = sourceCollection
          .orderBy(admin.firestore.FieldPath.documentId())
          .limit(this.config.migration_settings.batch_size);
        
        if (lastDoc) {
          query = query.startAfter(lastDoc);
        }
        
        const snapshot = await query.get();
        
        if (snapshot.empty) {
          break;
        }
        
        batchCount++;
        console.log(`   Processing batch ${batchCount} (${snapshot.docs.length} documents)...`);
        
        // Process documents in parallel batches
        const batch = this.targetDb.batch();
        const promises = [];
        
        for (const doc of snapshot.docs) {
          const docData = doc.data();
          
          // Apply special handling
          const processedData = await this.applySpecialHandling(docData, special_handling, name);
          
          // Add to batch
          const targetDocRef = targetCollection.doc(doc.id);
          batch.set(targetDocRef, processedData);
          
          documentCount++;
        }
        
        // Commit batch
        await batch.commit();
        
        console.log(`   ✅ Batch ${batchCount} completed (${documentCount} total documents)`);
        
        lastDoc = snapshot.docs[snapshot.docs.length - 1];
        
        // Add delay to avoid rate limiting
        await this.delay(100);
      }
      
      this.migrationStats.collections++;
      this.migrationStats.documents += documentCount;
      
      console.log(`✅ Collection ${name} migration completed: ${documentCount} documents`);
      
    } catch (error) {
      this.migrationStats.errors++;
      this.errors.push({
        collection: name,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      console.error(`❌ Failed to migrate collection ${name}:`, error.message);
      
      if (this.config.migration_settings.max_retries > 0) {
        console.log(`🔄 Retrying collection ${name}...`);
        await this.delay(this.config.migration_settings.retry_delay_ms);
        return this.migrateCollection(collectionConfig);
      }
      
      throw error;
    }
  }

  async applySpecialHandling(docData, specialHandling, collectionName) {
    let processedData = { ...docData };
    
    for (const handler of specialHandling) {
      switch (handler) {
        case 'preserve_auth_uid':
          // Ensure user ID consistency for auth-related documents
          if (collectionName === 'users' && processedData.id) {
            // Keep the original auth UID
            processedData.migratedFrom = this.config.source.projectId;
            processedData.migrationTimestamp = admin.firestore.FieldValue.serverTimestamp();
          }
          break;
          
        case 'validate_permissions':
          // Validate user permissions structure
          if (collectionName === 'users' && processedData.permissions) {
            processedData.permissions = this.validatePermissionsStructure(processedData.permissions);
          }
          break;
          
        case 'update_storage_references':
          // Update Firebase Storage references to new project
          if (processedData.filePath || processedData.downloadUrl) {
            processedData = await this.updateStorageReferences(processedData);
          }
          break;
          
        case 'validate_file_paths':
          // Validate and sanitize file paths
          if (processedData.fileName || processedData.filePath) {
            processedData = this.validateFilePaths(processedData);
          }
          break;
          
        case 'batch_processing':
          // Mark for batch processing (activities collection)
          processedData.migrationBatch = true;
          break;
          
        case 'date_validation':
          // Validate and convert date fields
          processedData = this.validateDateFields(processedData);
          break;
          
        case 'user_reference_validation':
          // Validate user references in favorites/other collections
          if (processedData.userId) {
            processedData.userReference = {
              originalProject: this.config.source.projectId,
              userId: processedData.userId
            };
          }
          break;
          
        case 'restore_capability':
          // Add restore metadata for recycle bin items
          if (collectionName === 'recycle_bin') {
            processedData.canRestore = true;
            processedData.migrationSource = this.config.source.projectId;
          }
          break;
      }
    }
    
    // Add migration metadata to all documents
    processedData._migration = {
      sourceProject: this.config.source.projectId,
      targetProject: this.config.target.projectId,
      migrationId: this.config.migration.id,
      migratedAt: admin.firestore.FieldValue.serverTimestamp()
    };
    
    return processedData;
  }

  validatePermissionsStructure(permissions) {
    const defaultPermissions = {
      documents: ['view'],
      categories: ['view'],
      system: []
    };
    
    return {
      documents: Array.isArray(permissions.documents) ? permissions.documents : defaultPermissions.documents,
      categories: Array.isArray(permissions.categories) ? permissions.categories : defaultPermissions.categories,
      system: Array.isArray(permissions.system) ? permissions.system : defaultPermissions.system
    };
  }

  async updateStorageReferences(docData) {
    const updatedData = { ...docData };
    
    // Update storage bucket references
    if (updatedData.downloadUrl) {
      updatedData.downloadUrl = updatedData.downloadUrl.replace(
        this.config.source.storageBucket,
        this.config.target.storageBucket
      );
    }
    
    if (updatedData.filePath) {
      // Keep the same file path structure
      updatedData.originalFilePath = updatedData.filePath;
    }
    
    return updatedData;
  }

  validateFilePaths(docData) {
    const updatedData = { ...docData };
    
    if (updatedData.fileName) {
      // Sanitize file name
      updatedData.fileName = updatedData.fileName.replace(/[^a-zA-Z0-9.-_]/g, '_');
    }
    
    if (updatedData.filePath) {
      // Validate file path format
      updatedData.filePath = updatedData.filePath.replace(/\/+/g, '/');
    }
    
    return updatedData;
  }

  validateDateFields(docData) {
    const updatedData = { ...docData };
    const dateFields = ['createdAt', 'updatedAt', 'uploadedAt', 'deletedAt', 'timestamp'];
    
    for (const field of dateFields) {
      if (updatedData[field]) {
        // Convert to Firestore timestamp if needed
        if (typeof updatedData[field] === 'string') {
          try {
            updatedData[field] = admin.firestore.Timestamp.fromDate(new Date(updatedData[field]));
          } catch (error) {
            console.warn(`Invalid date format for ${field}:`, updatedData[field]);
          }
        }
      }
    }
    
    return updatedData;
  }

  async generateMigrationReport() {
    const duration = this.migrationStats.endTime - this.migrationStats.startTime;
    const report = {
      migration_id: this.config.migration.id,
      source_project: this.config.source.projectId,
      target_project: this.config.target.projectId,
      start_time: this.migrationStats.startTime.toISOString(),
      end_time: this.migrationStats.endTime.toISOString(),
      duration_ms: duration,
      duration_formatted: this.formatDuration(duration),
      statistics: {
        collections_migrated: this.migrationStats.collections,
        total_documents: this.migrationStats.documents,
        errors_count: this.migrationStats.errors,
        success_rate: ((this.migrationStats.documents - this.migrationStats.errors) / this.migrationStats.documents * 100).toFixed(2) + '%'
      },
      errors: this.errors
    };
    
    const reportPath = path.join(__dirname, '../logs', `firestore-migration-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Migration Report Generated:');
    console.log(`   Collections: ${report.statistics.collections_migrated}`);
    console.log(`   Documents: ${report.statistics.total_documents}`);
    console.log(`   Errors: ${report.statistics.errors_count}`);
    console.log(`   Success Rate: ${report.statistics.success_rate}`);
    console.log(`   Duration: ${report.duration_formatted}`);
    console.log(`   Report saved: ${reportPath}`);
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run migration if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');

  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found. Run setup first: node config/setup-migration-config.js');
    process.exit(1);
  }

  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  const migration = new FirestoreMigration(config);

  migration.initialize()
    .then(() => migration.migrateAllCollections())
    .then(() => {
      console.log('\n🎉 Firestore migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Firestore migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = FirestoreMigration;
