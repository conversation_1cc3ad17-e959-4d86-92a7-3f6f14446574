#!/usr/bin/env node

/**
 * Firebase Backup System
 * 
 * Creates comprehensive backups of Firebase project data before migration
 * and provides restore capabilities in case of migration failure.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BackupSystem {
  constructor(config) {
    this.config = config;
    this.sourceDb = null;
    this.sourceAuth = null;
    this.sourceBucket = null;
    this.backupPath = null;
    this.backupStats = {
      collections: 0,
      documents: 0,
      users: 0,
      files: 0,
      totalSize: 0,
      startTime: null,
      endTime: null
    };
  }

  async initialize() {
    console.log('🔄 Initializing Backup System...');
    
    try {
      // Initialize source Firebase app
      const sourceServiceAccount = require(path.resolve(__dirname, this.config.source.serviceAccountPath));
      const sourceApp = admin.initializeApp({
        credential: admin.credential.cert(sourceServiceAccount),
        projectId: this.config.source.projectId,
        storageBucket: this.config.source.storageBucket
      }, 'source-backup');
      
      this.sourceDb = admin.firestore(sourceApp);
      this.sourceAuth = admin.auth(sourceApp);
      this.sourceBucket = admin.storage(sourceApp).bucket();
      
      // Create backup directory
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      this.backupPath = path.join(__dirname, '../backups', `backup-${timestamp}`);
      fs.mkdirSync(this.backupPath, { recursive: true });
      
      console.log('✅ Backup system initialized successfully');
      console.log(`📁 Backup location: ${this.backupPath}`);
      
    } catch (error) {
      throw new Error(`Failed to initialize backup system: ${error.message}`);
    }
  }

  async createFullBackup() {
    console.log('\n🚀 Starting full Firebase backup...');
    this.backupStats.startTime = new Date();
    
    try {
      // Create backup manifest
      const manifest = {
        backup_id: path.basename(this.backupPath),
        source_project: this.config.source.projectId,
        created_at: this.backupStats.startTime.toISOString(),
        backup_type: 'full',
        components: []
      };
      
      // Backup Firestore data
      if (this.config.migration_settings.backup_before_migration) {
        console.log('\n📊 Backing up Firestore data...');
        await this.backupFirestore();
        manifest.components.push('firestore');
      }
      
      // Backup Authentication users
      if (this.config.auth_migration.enabled) {
        console.log('\n👥 Backing up Authentication users...');
        await this.backupAuth();
        manifest.components.push('auth');
      }
      
      // Backup Storage files
      if (this.config.storage_migration.enabled) {
        console.log('\n📁 Backing up Storage files...');
        await this.backupStorage();
        manifest.components.push('storage');
      }
      
      // Backup Security Rules and Indexes
      console.log('\n🔒 Backing up Security Rules and Indexes...');
      await this.backupSecurityRules();
      manifest.components.push('security_rules');
      
      // Backup Functions
      if (this.config.functions_migration.enabled) {
        console.log('\n⚡ Backing up Functions...');
        await this.backupFunctions();
        manifest.components.push('functions');
      }
      
      this.backupStats.endTime = new Date();
      
      // Update and save manifest
      manifest.completed_at = this.backupStats.endTime.toISOString();
      manifest.duration_ms = this.backupStats.endTime - this.backupStats.startTime;
      manifest.statistics = this.backupStats;
      
      fs.writeFileSync(
        path.join(this.backupPath, 'backup-manifest.json'),
        JSON.stringify(manifest, null, 2)
      );
      
      await this.generateBackupReport();
      
      console.log('\n✅ Full backup completed successfully!');
      return this.backupPath;
      
    } catch (error) {
      this.backupStats.endTime = new Date();
      await this.generateBackupReport();
      throw error;
    }
  }

  async backupFirestore() {
    const firestorePath = path.join(this.backupPath, 'firestore');
    fs.mkdirSync(firestorePath, { recursive: true });
    
    // Get all collections
    const collections = await this.sourceDb.listCollections();
    
    for (const collection of collections) {
      const collectionName = collection.id;
      console.log(`   Backing up collection: ${collectionName}`);
      
      const collectionPath = path.join(firestorePath, `${collectionName}.json`);
      const documents = [];
      
      // Get all documents in collection
      const snapshot = await collection.get();
      
      snapshot.forEach(doc => {
        documents.push({
          id: doc.id,
          data: doc.data()
        });
      });
      
      fs.writeFileSync(collectionPath, JSON.stringify(documents, null, 2));
      
      this.backupStats.collections++;
      this.backupStats.documents += documents.length;
      
      console.log(`   ✅ Collection ${collectionName}: ${documents.length} documents`);
    }
  }

  async backupAuth() {
    const authPath = path.join(this.backupPath, 'auth');
    fs.mkdirSync(authPath, { recursive: true });
    
    const users = [];
    let nextPageToken;
    
    do {
      const listUsersResult = await this.sourceAuth.listUsers(1000, nextPageToken);
      
      for (const user of listUsersResult.users) {
        users.push({
          uid: user.uid,
          email: user.email,
          emailVerified: user.emailVerified,
          displayName: user.displayName,
          photoURL: user.photoURL,
          phoneNumber: user.phoneNumber,
          disabled: user.disabled,
          metadata: {
            creationTime: user.metadata.creationTime,
            lastSignInTime: user.metadata.lastSignInTime
          },
          customClaims: user.customClaims,
          providerData: user.providerData
        });
      }
      
      nextPageToken = listUsersResult.pageToken;
      
    } while (nextPageToken);
    
    fs.writeFileSync(
      path.join(authPath, 'users.json'),
      JSON.stringify(users, null, 2)
    );
    
    this.backupStats.users = users.length;
    console.log(`   ✅ Authentication: ${users.length} users`);
  }

  async backupStorage() {
    const storagePath = path.join(this.backupPath, 'storage');
    fs.mkdirSync(storagePath, { recursive: true });
    
    const filesList = [];
    
    for (const folder of this.config.storage_migration.folders_to_migrate) {
      console.log(`   Backing up storage folder: ${folder}`);
      
      const [files] = await this.sourceBucket.getFiles({
        prefix: folder
      });
      
      for (const file of files) {
        const [metadata] = await file.getMetadata();
        
        filesList.push({
          name: file.name,
          bucket: file.bucket.name,
          size: metadata.size,
          contentType: metadata.contentType,
          timeCreated: metadata.timeCreated,
          updated: metadata.updated,
          md5Hash: metadata.md5Hash,
          metadata: metadata.metadata
        });
        
        this.backupStats.files++;
        this.backupStats.totalSize += parseInt(metadata.size);
      }
    }
    
    fs.writeFileSync(
      path.join(storagePath, 'files-manifest.json'),
      JSON.stringify(filesList, null, 2)
    );
    
    console.log(`   ✅ Storage: ${filesList.length} files (${this.formatBytes(this.backupStats.totalSize)})`);
  }

  async backupSecurityRules() {
    const rulesPath = path.join(this.backupPath, 'security_rules');
    fs.mkdirSync(rulesPath, { recursive: true });
    
    try {
      // Copy Firestore rules
      const firestoreRulesSource = path.resolve(__dirname, '../../../firestore.rules');
      if (fs.existsSync(firestoreRulesSource)) {
        fs.copyFileSync(firestoreRulesSource, path.join(rulesPath, 'firestore.rules'));
      }
      
      // Copy Storage rules
      const storageRulesSource = path.resolve(__dirname, '../../../storage.rules');
      if (fs.existsSync(storageRulesSource)) {
        fs.copyFileSync(storageRulesSource, path.join(rulesPath, 'storage.rules'));
      }
      
      // Copy Firestore indexes
      const indexesSource = path.resolve(__dirname, '../../../firestore.indexes.json');
      if (fs.existsSync(indexesSource)) {
        fs.copyFileSync(indexesSource, path.join(rulesPath, 'firestore.indexes.json'));
      }
      
      console.log('   ✅ Security rules and indexes backed up');
      
    } catch (error) {
      console.warn('   ⚠️  Could not backup security rules:', error.message);
    }
  }

  async backupFunctions() {
    const functionsPath = path.join(this.backupPath, 'functions');
    fs.mkdirSync(functionsPath, { recursive: true });
    
    try {
      // Copy functions source code
      const functionsSource = path.resolve(__dirname, '../../../functions');
      if (fs.existsSync(functionsSource)) {
        this.copyDirectory(functionsSource, functionsPath);
      }
      
      console.log('   ✅ Functions source code backed up');
      
    } catch (error) {
      console.warn('   ⚠️  Could not backup functions:', error.message);
    }
  }

  copyDirectory(source, destination) {
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }
    
    const items = fs.readdirSync(source);
    
    for (const item of items) {
      const sourcePath = path.join(source, item);
      const destPath = path.join(destination, item);
      
      if (item === 'node_modules' || item === '.git') {
        continue; // Skip node_modules and .git directories
      }
      
      const stat = fs.statSync(sourcePath);
      
      if (stat.isDirectory()) {
        this.copyDirectory(sourcePath, destPath);
      } else {
        fs.copyFileSync(sourcePath, destPath);
      }
    }
  }

  async generateBackupReport() {
    const duration = this.backupStats.endTime - this.backupStats.startTime;
    const report = {
      backup_id: path.basename(this.backupPath),
      source_project: this.config.source.projectId,
      backup_path: this.backupPath,
      start_time: this.backupStats.startTime.toISOString(),
      end_time: this.backupStats.endTime?.toISOString(),
      duration_ms: duration,
      duration_formatted: this.formatDuration(duration),
      statistics: this.backupStats
    };
    
    const reportPath = path.join(this.backupPath, 'backup-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Backup Report Generated:');
    console.log(`   Collections: ${report.statistics.collections}`);
    console.log(`   Documents: ${report.statistics.documents}`);
    console.log(`   Users: ${report.statistics.users}`);
    console.log(`   Files: ${report.statistics.files}`);
    console.log(`   Total Size: ${this.formatBytes(report.statistics.totalSize)}`);
    console.log(`   Duration: ${report.duration_formatted}`);
    console.log(`   Backup Path: ${this.backupPath}`);
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}

// Run backup if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found. Run setup first: node config/setup-migration-config.js');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  const backup = new BackupSystem(config);
  
  backup.initialize()
    .then(() => backup.createFullBackup())
    .then((backupPath) => {
      console.log(`\n🎉 Backup completed successfully!`);
      console.log(`📁 Backup saved to: ${backupPath}`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Backup failed:', error.message);
      process.exit(1);
    });
}

module.exports = BackupSystem;
