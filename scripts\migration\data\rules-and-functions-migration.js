#!/usr/bin/env node

/**
 * Security Rules and Functions Migration
 * 
 * Migrates Firestore security rules, indexes, and Firebase Functions
 * to the new Firebase project with proper validation and deployment.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class RulesAndFunctionsMigration {
  constructor(config) {
    this.config = config;
    this.projectRoot = path.resolve(__dirname, '../../../..');
    this.migrationStats = {
      rules_deployed: 0,
      indexes_deployed: 0,
      functions_deployed: 0,
      errors: 0,
      startTime: null,
      endTime: null
    };
    this.errors = [];
  }

  async migrateAll() {
    console.log('🔒 Starting Security Rules and Functions Migration...');
    console.log('==================================================\n');
    
    try {
      this.migrationStats.startTime = new Date();
      
      // Set target project
      await this.setTargetProject();
      
      // Update project references in rules and functions
      await this.updateProjectReferences();
      
      // Deploy Firestore security rules
      await this.deployFirestoreRules();
      
      // Deploy Firestore indexes
      await this.deployFirestoreIndexes();
      
      // Deploy Storage security rules
      await this.deployStorageRules();
      
      // Deploy Firebase Functions
      if (this.config.functions_migration.enabled) {
        await this.deployFunctions();
      }
      
      // Verify deployments
      await this.verifyDeployments();
      
      this.migrationStats.endTime = new Date();
      await this.generateMigrationReport();
      
      console.log('\n✅ Security Rules and Functions migration completed successfully!');
      
    } catch (error) {
      this.migrationStats.endTime = new Date();
      await this.generateMigrationReport();
      throw error;
    }
  }

  async setTargetProject() {
    console.log('🎯 Setting target Firebase project...');
    
    try {
      // Set the target project as default
      execSync(`firebase use ${this.config.target.projectId}`, {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      console.log(`   ✅ Target project set: ${this.config.target.projectId}`);
      
    } catch (error) {
      throw new Error(`Failed to set target project: ${error.message}`);
    }
  }

  async updateProjectReferences() {
    console.log('🔄 Updating project references...');
    
    // Update Firestore rules
    await this.updateFirestoreRulesReferences();
    
    // Update Functions code
    await this.updateFunctionsReferences();
    
    // Update configuration files
    await this.updateConfigurationFiles();
    
    console.log('   ✅ Project references updated');
  }

  async updateFirestoreRulesReferences() {
    const rulesPath = path.join(this.projectRoot, 'firestore.rules');
    
    if (fs.existsSync(rulesPath)) {
      let rulesContent = fs.readFileSync(rulesPath, 'utf8');
      
      // Update any project-specific references in rules
      rulesContent = rulesContent.replace(
        new RegExp(this.config.source.projectId, 'g'),
        this.config.target.projectId
      );
      
      // Backup original rules
      fs.copyFileSync(rulesPath, rulesPath + '.backup-' + Date.now());
      
      // Write updated rules
      fs.writeFileSync(rulesPath, rulesContent);
      
      console.log('   ✅ Firestore rules updated');
    }
  }

  async updateFunctionsReferences() {
    const functionsPath = path.join(this.projectRoot, 'functions');
    
    if (!fs.existsSync(functionsPath)) {
      console.log('   ⚠️  Functions directory not found, skipping...');
      return;
    }
    
    // Update functions/src/index.ts
    const indexPath = path.join(functionsPath, 'src', 'index.ts');
    if (fs.existsSync(indexPath)) {
      let indexContent = fs.readFileSync(indexPath, 'utf8');
      
      // Backup original file
      fs.copyFileSync(indexPath, indexPath + '.backup-' + Date.now());
      
      // Update project ID references
      indexContent = indexContent.replace(
        new RegExp(this.config.source.projectId, 'g'),
        this.config.target.projectId
      );
      
      // Update storage bucket references
      indexContent = indexContent.replace(
        new RegExp(this.config.source.storageBucket, 'g'),
        this.config.target.storageBucket
      );
      
      fs.writeFileSync(indexPath, indexContent);
      console.log('   ✅ Functions index.ts updated');
    }
    
    // Update other function files if they contain project references
    await this.updateFunctionModules(functionsPath);
  }

  async updateFunctionModules(functionsPath) {
    const modulesPath = path.join(functionsPath, 'src', 'modules');
    
    if (!fs.existsSync(modulesPath)) {
      return;
    }
    
    const moduleFiles = fs.readdirSync(modulesPath);
    
    for (const moduleFile of moduleFiles) {
      if (!moduleFile.endsWith('.ts') && !moduleFile.endsWith('.js')) {
        continue;
      }
      
      const modulePath = path.join(modulesPath, moduleFile);
      let moduleContent = fs.readFileSync(modulePath, 'utf8');
      
      // Check if file contains project references
      if (moduleContent.includes(this.config.source.projectId) || 
          moduleContent.includes(this.config.source.storageBucket)) {
        
        // Backup original file
        fs.copyFileSync(modulePath, modulePath + '.backup-' + Date.now());
        
        // Update references
        moduleContent = moduleContent.replace(
          new RegExp(this.config.source.projectId, 'g'),
          this.config.target.projectId
        );
        
        moduleContent = moduleContent.replace(
          new RegExp(this.config.source.storageBucket, 'g'),
          this.config.target.storageBucket
        );
        
        fs.writeFileSync(modulePath, moduleContent);
        console.log(`   ✅ Function module ${moduleFile} updated`);
      }
    }
  }

  async updateConfigurationFiles() {
    // Update .firebaserc
    const firebaseRCPath = path.join(this.projectRoot, '.firebaserc');
    if (fs.existsSync(firebaseRCPath)) {
      // Backup original file
      fs.copyFileSync(firebaseRCPath, firebaseRCPath + '.backup-' + Date.now());
      
      const firebaseRC = {
        "projects": {
          "default": this.config.target.projectId,
          "doc": this.config.target.projectId
        },
        "targets": {},
        "etags": {}
      };
      
      fs.writeFileSync(firebaseRCPath, JSON.stringify(firebaseRC, null, 2));
      console.log('   ✅ .firebaserc updated');
    }
  }

  async deployFirestoreRules() {
    console.log('🔒 Deploying Firestore security rules...');
    
    try {
      execSync('firebase deploy --only firestore:rules', {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      this.migrationStats.rules_deployed++;
      console.log('   ✅ Firestore rules deployed successfully');
      
    } catch (error) {
      this.migrationStats.errors++;
      this.errors.push({
        component: 'firestore_rules',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw new Error(`Failed to deploy Firestore rules: ${error.message}`);
    }
  }

  async deployFirestoreIndexes() {
    console.log('📊 Deploying Firestore indexes...');
    
    try {
      execSync('firebase deploy --only firestore:indexes', {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      this.migrationStats.indexes_deployed++;
      console.log('   ✅ Firestore indexes deployed successfully');
      
    } catch (error) {
      this.migrationStats.errors++;
      this.errors.push({
        component: 'firestore_indexes',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw new Error(`Failed to deploy Firestore indexes: ${error.message}`);
    }
  }

  async deployStorageRules() {
    console.log('📁 Deploying Storage security rules...');
    
    try {
      execSync('firebase deploy --only storage', {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      this.migrationStats.rules_deployed++;
      console.log('   ✅ Storage rules deployed successfully');
      
    } catch (error) {
      this.migrationStats.errors++;
      this.errors.push({
        component: 'storage_rules',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw new Error(`Failed to deploy Storage rules: ${error.message}`);
    }
  }

  async deployFunctions() {
    console.log('⚡ Deploying Firebase Functions...');
    
    try {
      // Install dependencies first
      console.log('   📦 Installing function dependencies...');
      execSync('npm install', {
        cwd: path.join(this.projectRoot, 'functions'),
        stdio: 'pipe'
      });
      
      // Build functions
      console.log('   🔨 Building functions...');
      execSync('npm run build', {
        cwd: path.join(this.projectRoot, 'functions'),
        stdio: 'pipe'
      });
      
      // Deploy functions
      console.log('   🚀 Deploying functions...');
      execSync('firebase deploy --only functions', {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      this.migrationStats.functions_deployed = this.config.functions_migration.functions_to_deploy.length;
      console.log(`   ✅ Functions deployed successfully (${this.migrationStats.functions_deployed} functions)`);
      
    } catch (error) {
      this.migrationStats.errors++;
      this.errors.push({
        component: 'functions',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw new Error(`Failed to deploy Functions: ${error.message}`);
    }
  }

  async verifyDeployments() {
    console.log('🔍 Verifying deployments...');
    
    // Verify Firestore rules
    await this.verifyFirestoreRules();
    
    // Verify Functions
    if (this.config.functions_migration.enabled) {
      await this.verifyFunctions();
    }
    
    console.log('   ✅ All deployments verified');
  }

  async verifyFirestoreRules() {
    try {
      // Test basic Firestore access to verify rules are working
      const admin = require('firebase-admin');
      
      // Initialize app for verification
      const serviceAccount = require(path.resolve(__dirname, this.config.target.serviceAccountPath));
      const app = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: this.config.target.projectId
      }, 'rules-verify');
      
      const db = admin.firestore(app);
      
      // Test basic read operation
      await db.collection('users').limit(1).get();
      
      console.log('   ✅ Firestore rules verification passed');
      
    } catch (error) {
      console.warn(`   ⚠️  Firestore rules verification warning: ${error.message}`);
    }
  }

  async verifyFunctions() {
    try {
      // List deployed functions
      const functionsList = execSync('firebase functions:list', {
        cwd: this.projectRoot,
        encoding: 'utf8'
      });
      
      // Check if expected functions are deployed
      const expectedFunctions = this.config.functions_migration.functions_to_deploy;
      const missingFunctions = [];
      
      for (const expectedFunction of expectedFunctions) {
        if (!functionsList.includes(expectedFunction)) {
          missingFunctions.push(expectedFunction);
        }
      }
      
      if (missingFunctions.length > 0) {
        throw new Error(`Missing functions: ${missingFunctions.join(', ')}`);
      }
      
      console.log('   ✅ Functions verification passed');
      
    } catch (error) {
      console.warn(`   ⚠️  Functions verification warning: ${error.message}`);
    }
  }

  async generateMigrationReport() {
    const duration = this.migrationStats.endTime - this.migrationStats.startTime;
    
    const report = {
      migration_id: this.config.migration.id,
      target_project: this.config.target.projectId,
      start_time: this.migrationStats.startTime.toISOString(),
      end_time: this.migrationStats.endTime?.toISOString(),
      duration_ms: duration,
      duration_formatted: this.formatDuration(duration),
      statistics: this.migrationStats,
      errors: this.errors,
      deployed_components: {
        firestore_rules: this.migrationStats.rules_deployed > 0,
        firestore_indexes: this.migrationStats.indexes_deployed > 0,
        functions: this.migrationStats.functions_deployed > 0
      }
    };
    
    const reportPath = path.join(__dirname, '../logs', `rules-functions-migration-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Rules and Functions Migration Report:');
    console.log(`   Rules deployed: ${report.statistics.rules_deployed}`);
    console.log(`   Indexes deployed: ${report.statistics.indexes_deployed}`);
    console.log(`   Functions deployed: ${report.statistics.functions_deployed}`);
    console.log(`   Errors: ${report.statistics.errors}`);
    console.log(`   Duration: ${report.duration_formatted}`);
    console.log(`   Report saved: ${reportPath}`);
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found. Run setup first: node config/setup-migration-config.js');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  const migration = new RulesAndFunctionsMigration(config);
  
  migration.migrateAll()
    .then(() => {
      console.log('\n🎉 Rules and Functions migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Rules and Functions migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = RulesAndFunctionsMigration;
