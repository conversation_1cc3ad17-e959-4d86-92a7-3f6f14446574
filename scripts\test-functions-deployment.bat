@echo off
echo ========================================
echo 🧪 TEST FIREBASE FUNCTIONS DEPLOYMENT
echo ========================================
echo.

echo 🔄 Testing health check function...
firebase functions:shell --project document-management-c5a96 <<EOF
healthCheck()
EOF

echo.
echo 📋 Listing all deployed functions...
firebase functions:list --project document-management-c5a96

echo.
echo 📊 Expected ACTIVE functions (11 total):
echo   ✅ hybridProcessFileUpload - Core file processing
echo   ✅ getFileAccessUrl - Secure URL generation
echo   ✅ createCategory - Admin category management
echo   ✅ updateCategory - Admin category updates
echo   ✅ deleteCategory - Admin category deletion
echo   ✅ deleteDocument - Atomic document deletion
echo   ✅ createUser - Admin user creation
echo   ✅ logActivity - Security audit logging
echo   ✅ validateUserSession - Session validation
echo   ✅ getActivityStatistics - Analytics
echo   ✅ healthCheck - System monitoring

echo.
echo ❌ Functions that should be REMOVED (if still present):
echo   ❌ streamingUpload
echo   ❌ getStorageQuota
echo   ❌ cleanupOrphanedFiles
echo   ❌ batchProcessFiles
echo   ❌ updateUserPermissions
echo   ❌ deleteUser
echo   ❌ bulkUserOperations
echo   ❌ setAdminClaims
echo   ❌ autoSyncFirebaseAuthUsers
echo   ❌ debugAuthPermissions
echo   ❌ initializeAdmin
echo   ❌ bulkDocumentOperations
echo   ❌ generateDocumentReport
echo   ❌ All sync operations functions
echo   ❌ All notification functions
echo   ❌ All real-time sync functions

echo.
echo 🔍 Check Firebase Console for function logs:
echo https://console.firebase.google.com/project/document-management-c5a96/functions

pause
