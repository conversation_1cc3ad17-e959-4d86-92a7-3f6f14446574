# 🛠️ Scripts Directory

This directory contains deployment and utility scripts for the Firebase Functions project.

## 📁 Directory Structure

```
scripts/
├── config/
│   ├── service-account-key.json     # Firebase service account credentials
│   └── migration/                   # Migration configuration files
├── migration/                       # Firebase migration system
│   ├── data/                        # Data migration scripts
│   ├── config/                      # Configuration update scripts
│   ├── verification/                # Verification and testing scripts
│   ├── orchestration/               # Main migration orchestration
│   └── docs/                        # Migration documentation
├── deploy-firebase-clean.bat        # Main deployment script
├── test-functions-deployment.bat    # Test deployed functions
├── remove-unused-functions.bat      # Remove orphaned functions
└── README.md                        # This file
```

## 🚀 Usage Instructions

### 1. Initial Firebase Setup & Deployment

Run this script for first-time setup or complete reinstallation:

```bash
# From project root directory
scripts/deploy-firebase-clean.bat
```

**What it does:**
- Installs Firebase CLI globally
- Logs into Firebase
- Sets the correct Firebase project
- Installs function dependencies
- Builds TypeScript functions
- Deploys to Firebase
- Verifies deployment

### 2. Test Deployment

After deployment, verify functions are working:

```bash
# From project root directory  
scripts/test-functions-deployment.bat
```

**What it does:**
- Tests health check function
- Lists all deployed functions
- Shows expected vs actual functions
- Provides Firebase Console link

### 3. Remove Unused Functions (Optional)

Clean up old functions from Firebase:

```bash
# From project root directory
scripts/remove-unused-functions.bat
```

**What it does:**
- Removes 36 unused/orphaned functions
- Keeps only 11 active functions
- Requires confirmation before deletion
- Shows final function list

## 📋 Active Functions (11 Total)

After cleanup, these functions should be deployed:

### Core Functions:
- `hybridProcessFileUpload` - Main file processing
- `getFileAccessUrl` - Secure URL generation
- `healthCheck` - System monitoring

### Admin Functions:
- `createCategory`, `updateCategory`, `deleteCategory` - Category management
- `deleteDocument` - Document deletion
- `createUser` - User creation

### Security Functions:
- `logActivity` - Activity logging
- `validateUserSession` - Session validation

### Analytics Functions:
- `getActivityStatistics` - Activity statistics

## 🔧 Configuration

### Service Account Key
The `config/service-account-key.json` file contains Firebase Admin SDK credentials. This file should:
- ✅ Be kept secure and not committed to version control
- ✅ Have proper Firebase Admin permissions
- ✅ Be downloaded from Firebase Console > Project Settings > Service Accounts

### Script Execution
All scripts should be run from the **project root directory**, not from the scripts directory:

```bash
# ✅ Correct - from project root
scripts/deploy-firebase-clean.bat

# ❌ Wrong - from scripts directory
cd scripts
deploy-firebase-clean.bat
```

## 🔍 Troubleshooting

### Common Issues:

1. **Firebase CLI not found**
   - Run: `npm install -g firebase-tools`

2. **Permission denied**
   - Run: `firebase login`
   - Ensure correct Firebase project access

3. **Build errors**
   - Check TypeScript compilation: `cd functions && npm run build`
   - Verify dependencies: `cd functions && npm install`

4. **Deployment failures**
   - Check Firebase project: `firebase use document-management-c5a96`
   - Verify service account permissions

## 📚 Related Documentation

See the `docs/` folder for detailed documentation:
- `docs/FIREBASE_FUNCTIONS_CLEANED.md` - Cleanup details
- `docs/BACKUP_CURRENT_STRUCTURE.md` - Original structure backup
