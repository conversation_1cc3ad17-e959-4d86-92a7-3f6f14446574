@echo off
echo ========================================
echo 🗑️ REMOVE UNUSED FIREBASE FUNCTIONS
echo ========================================
echo.
echo ⚠️  PERINGATAN: Script ini akan menghapus 36 functions yang tidak digunakan
echo     dari Firebase deployment. Pastikan Anda sudah backup!
echo.
echo 📋 Functions yang akan dihapus:
echo   ❌ streamingUpload
echo   ❌ getStorageQuota
echo   ❌ cleanupOrphanedFiles
echo   ❌ batchProcessFiles
echo   ❌ updateUserPermissions
echo   ❌ deleteUser
echo   ❌ bulkUserOperations
echo   ❌ setAdminClaims
echo   ❌ autoSyncFirebaseAuthUsers
echo   ❌ debugAuthPermissions
echo   ❌ initializeAdmin
echo   ❌ bulkDocumentOperations
echo   ❌ generateDocumentReport
echo   ❌ syncStorageWithFirestore
echo   ❌ syncStorageToFirestore
echo   ❌ cleanupOrphanedMetadata
echo   ❌ performComprehensiveSync
echo   ❌ monitorSyncConsistency
echo   ❌ repairSyncInconsistencies
echo   ❌ getAggregatedStatistics
echo   ❌ getPaginatedFileStats
echo   ❌ sendNotification
echo   ❌ processActivityLog
echo   ❌ onStorageFileCreated
echo   ❌ onStorageFileDeleted
echo   ❌ onAuthUserCreated
echo   ❌ onAuthUserDeleted
echo   ❌ onDocumentCreate
echo   ❌ onUserCreate
echo   ❌ onFileUpload
echo   ❌ manualCleanupActivityLogs
echo   ❌ api
echo.

set /p confirm="Lanjutkan menghapus functions? (y/N): "
if /i not "%confirm%"=="y" (
    echo ❌ Operasi dibatalkan
    pause
    exit /b 0
)

echo.
echo 🔄 Menghapus functions yang tidak digunakan...

REM File Upload Functions
firebase functions:delete streamingUpload --force --project document-management-c5a96
firebase functions:delete getStorageQuota --force --project document-management-c5a96
firebase functions:delete cleanupOrphanedFiles --force --project document-management-c5a96
firebase functions:delete batchProcessFiles --force --project document-management-c5a96

REM User Management Functions
firebase functions:delete updateUserPermissions --force --project document-management-c5a96
firebase functions:delete deleteUser --force --project document-management-c5a96
firebase functions:delete bulkUserOperations --force --project document-management-c5a96
firebase functions:delete setAdminClaims --force --project document-management-c5a96
firebase functions:delete autoSyncFirebaseAuthUsers --force --project document-management-c5a96
firebase functions:delete debugAuthPermissions --force --project document-management-c5a96
firebase functions:delete initializeAdmin --force --project document-management-c5a96

REM Document Management Functions
firebase functions:delete bulkDocumentOperations --force --project document-management-c5a96
firebase functions:delete generateDocumentReport --force --project document-management-c5a96

REM Sync Operations Functions
firebase functions:delete syncStorageWithFirestore --force --project document-management-c5a96
firebase functions:delete syncStorageToFirestore --force --project document-management-c5a96
firebase functions:delete cleanupOrphanedMetadata --force --project document-management-c5a96
firebase functions:delete performComprehensiveSync --force --project document-management-c5a96
firebase functions:delete monitorSyncConsistency --force --project document-management-c5a96
firebase functions:delete repairSyncInconsistencies --force --project document-management-c5a96
firebase functions:delete getAggregatedStatistics --force --project document-management-c5a96
firebase functions:delete getPaginatedFileStats --force --project document-management-c5a96

REM Notification Functions
firebase functions:delete sendNotification --force --project document-management-c5a96
firebase functions:delete processActivityLog --force --project document-management-c5a96

REM Real-time Sync Functions
firebase functions:delete onStorageFileCreated --force --project document-management-c5a96
firebase functions:delete onStorageFileDeleted --force --project document-management-c5a96
firebase functions:delete onAuthUserCreated --force --project document-management-c5a96
firebase functions:delete onAuthUserDeleted --force --project document-management-c5a96

REM Triggers & Others
firebase functions:delete onDocumentCreate --force --project document-management-c5a96
firebase functions:delete onUserCreate --force --project document-management-c5a96
firebase functions:delete onFileUpload --force --project document-management-c5a96
firebase functions:delete manualCleanupActivityLogs --force --project document-management-c5a96
firebase functions:delete api --force --project document-management-c5a96

echo.
echo ✅ Selesai menghapus functions yang tidak digunakan!
echo.
echo 📋 Cek functions yang masih aktif:
firebase functions:list --project document-management-c5a96

pause
