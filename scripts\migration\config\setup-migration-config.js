#!/usr/bin/env node

/**
 * Firebase Migration Configuration Setup
 * 
 * This script helps set up the migration configuration by:
 * 1. Prompting for target Firebase project details
 * 2. Validating source and target project access
 * 3. Generating configuration files
 * 4. Setting up service account keys
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const admin = require('firebase-admin');

class MigrationConfigSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    this.configPath = path.join(__dirname, 'migration-config.json');
    this.sourceConfigPath = path.join(__dirname, 'source-project.json');
    this.targetConfigPath = path.join(__dirname, 'target-project.json');
  }

  async setup() {
    console.log('🔄 Firebase Migration Configuration Setup');
    console.log('==========================================\n');

    try {
      // Step 1: Load base configuration
      const baseConfig = await this.loadBaseConfig();
      
      // Step 2: Get target project details
      const targetProject = await this.getTargetProjectDetails();
      
      // Step 3: Validate source project access
      await this.validateSourceProject(baseConfig.source);
      
      // Step 4: Validate target project access
      await this.validateTargetProject(targetProject);
      
      // Step 5: Generate final configuration
      const finalConfig = await this.generateFinalConfig(baseConfig, targetProject);
      
      // Step 6: Save configuration files
      await this.saveConfigurations(finalConfig, targetProject);
      
      console.log('\n✅ Migration configuration setup completed successfully!');
      console.log('\nNext steps:');
      console.log('1. Review the generated configuration files');
      console.log('2. Place target project service account key in config/');
      console.log('3. Run pre-migration checks: node orchestration/pre-migration-checks.js');
      
    } catch (error) {
      console.error('\n❌ Setup failed:', error.message);
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  async loadBaseConfig() {
    console.log('📋 Loading base configuration...');
    
    if (!fs.existsSync(this.configPath)) {
      throw new Error('Base migration-config.json not found');
    }
    
    const configContent = fs.readFileSync(this.configPath, 'utf8');
    return JSON.parse(configContent);
  }

  async getTargetProjectDetails() {
    console.log('\n🎯 Target Firebase Project Configuration');
    console.log('Please provide details for your new Firebase project:\n');

    const projectId = await this.prompt('Target Project ID: ');
    const projectNumber = await this.prompt('Target Project Number: ');
    const region = await this.prompt('Target Project Region (default: us-central1): ') || 'us-central1';
    
    return {
      projectId: projectId.trim(),
      projectNumber: projectNumber.trim(),
      storageBucket: `${projectId.trim()}.firebasestorage.app`,
      region: region.trim(),
      serviceAccountPath: './config/target-service-account-key.json',
      description: 'New production Firebase project'
    };
  }

  async validateSourceProject(sourceConfig) {
    console.log('\n🔍 Validating source project access...');
    
    try {
      // Initialize source project admin
      const serviceAccountPath = path.resolve(__dirname, '../../config/service-account-key.json');
      
      if (!fs.existsSync(serviceAccountPath)) {
        throw new Error('Source project service account key not found');
      }
      
      const serviceAccount = require(serviceAccountPath);
      
      if (admin.apps.length === 0) {
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          projectId: sourceConfig.projectId,
          storageBucket: sourceConfig.storageBucket
        }, 'source');
      }
      
      // Test Firestore access
      const db = admin.firestore(admin.app('source'));
      await db.collection('users').limit(1).get();
      
      // Test Storage access
      const bucket = admin.storage(admin.app('source')).bucket();
      await bucket.getMetadata();
      
      console.log('✅ Source project access validated');
      
    } catch (error) {
      throw new Error(`Source project validation failed: ${error.message}`);
    }
  }

  async validateTargetProject(targetConfig) {
    console.log('\n🎯 Validating target project access...');
    
    const serviceAccountPath = path.join(__dirname, 'target-service-account-key.json');
    
    if (!fs.existsSync(serviceAccountPath)) {
      console.log('⚠️  Target service account key not found.');
      console.log('Please download the service account key for your target project and place it at:');
      console.log(`   ${serviceAccountPath}`);
      
      const proceed = await this.prompt('Continue without validation? (y/N): ');
      if (proceed.toLowerCase() !== 'y') {
        throw new Error('Target project validation skipped');
      }
      
      console.log('⚠️  Skipping target project validation');
      return;
    }
    
    try {
      const serviceAccount = require(serviceAccountPath);
      
      // Initialize target project admin
      const targetApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: targetConfig.projectId,
        storageBucket: targetConfig.storageBucket
      }, 'target');
      
      // Test basic access
      const db = admin.firestore(targetApp);
      await db.collection('_test').limit(1).get();
      
      console.log('✅ Target project access validated');
      
    } catch (error) {
      throw new Error(`Target project validation failed: ${error.message}`);
    }
  }

  async generateFinalConfig(baseConfig, targetProject) {
    console.log('\n⚙️  Generating final configuration...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Update configuration with target project details and timestamp
    const finalConfig = JSON.parse(JSON.stringify(baseConfig));
    
    finalConfig.migration.id = finalConfig.migration.id.replace('{{timestamp}}', timestamp);
    finalConfig.migration.created = new Date().toISOString();
    finalConfig.target = targetProject;
    finalConfig.logging.log_file = finalConfig.logging.log_file.replace('{{timestamp}}', timestamp);
    finalConfig.rollback.backup_location = finalConfig.rollback.backup_location.replace('{{timestamp}}', timestamp);
    
    return finalConfig;
  }

  async saveConfigurations(finalConfig, targetProject) {
    console.log('\n💾 Saving configuration files...');
    
    // Save main configuration
    fs.writeFileSync(this.configPath, JSON.stringify(finalConfig, null, 2));
    console.log('✅ Updated migration-config.json');
    
    // Save source project config
    fs.writeFileSync(this.sourceConfigPath, JSON.stringify(finalConfig.source, null, 2));
    console.log('✅ Created source-project.json');
    
    // Save target project config
    fs.writeFileSync(this.targetConfigPath, JSON.stringify(targetProject, null, 2));
    console.log('✅ Created target-project.json');
    
    // Create logs directory
    const logsDir = path.join(__dirname, '../logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
      console.log('✅ Created logs directory');
    }
    
    // Create backups directory
    const backupsDir = path.join(__dirname, '../backups');
    if (!fs.existsSync(backupsDir)) {
      fs.mkdirSync(backupsDir, { recursive: true });
      console.log('✅ Created backups directory');
    }
  }

  async prompt(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new MigrationConfigSetup();
  setup.setup().catch(console.error);
}

module.exports = MigrationConfigSetup;
