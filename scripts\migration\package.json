{"name": "firebase-migration-system", "version": "1.0.0", "description": "Automated Firebase project migration system for SIMDOC document management application", "main": "orchestration/migration-orchestrator.js", "scripts": {"setup": "node config/setup-migration-config.js", "pre-check": "node orchestration/pre-migration-checks.js", "migrate": "node orchestration/migration-orchestrator.js", "verify": "node verification/data-integrity-checker.js", "post-check": "node orchestration/post-migration-checks.js", "backup": "node data/backup-system.js", "rollback": "node orchestration/rollback-system.js", "update-config": "node config/flutter-config-updater.js", "generate-configs": "node config/platform-config-generator.js", "migrate-firestore": "node data/firestore-migration.js", "migrate-auth": "node data/auth-migration.js", "migrate-storage": "node data/storage-migration.js", "migrate-rules": "node data/rules-and-functions-migration.js", "full-migration": "npm run pre-check && npm run migrate && npm run verify", "test-migration": "npm run setup && npm run pre-check", "help": "echo 'Available commands: setup, pre-check, migrate, verify, post-check, backup, rollback, update-config, generate-configs'"}, "keywords": ["firebase", "migration", "firestore", "authentication", "storage", "functions", "flutter", "automation"], "author": "SIMDOC Development Team", "license": "MIT", "dependencies": {"firebase-admin": "^12.0.0"}, "devDependencies": {}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/simdoc-migration"}, "bugs": {"url": "https://github.com/your-org/simdoc-migration/issues"}, "homepage": "https://github.com/your-org/simdoc-migration#readme"}