{"migration": {"id": "firebase-migration-{{timestamp}}", "name": "SIMDOC Firebase Project Migration", "description": "Automated migration from trial project to production project", "version": "1.0.0", "created": "{{timestamp}}", "status": "pending"}, "source": {"projectId": "document-management-c5a96", "projectNumber": "************", "storageBucket": "document-management-c5a96.firebasestorage.app", "region": "us-central1", "serviceAccountPath": "../config/service-account-key.json", "description": "Current trial Firebase project"}, "target": {"projectId": "{{TARGET_PROJECT_ID}}", "projectNumber": "{{TARGET_PROJECT_NUMBER}}", "storageBucket": "{{TARGET_PROJECT_ID}}.firebasestorage.app", "region": "us-central1", "serviceAccountPath": "./config/target-service-account-key.json", "description": "New production Firebase project"}, "migration_settings": {"batch_size": 500, "max_retries": 3, "retry_delay_ms": 1000, "parallel_operations": 5, "backup_before_migration": true, "verify_after_migration": true, "enable_rollback": true, "preserve_timestamps": true, "migrate_user_passwords": false, "update_security_rules": true, "deploy_functions": true}, "collections_to_migrate": [{"name": "users", "priority": 1, "description": "User profiles and permissions", "estimated_documents": 100, "special_handling": ["preserve_auth_uid", "validate_permissions"]}, {"name": "categories", "priority": 2, "description": "Document categories", "estimated_documents": 50, "special_handling": ["validate_structure"]}, {"name": "documents", "priority": 3, "description": "Document metadata", "estimated_documents": 10000, "special_handling": ["update_storage_references", "validate_file_paths"]}, {"name": "activities", "priority": 4, "description": "User activity logs", "estimated_documents": 50000, "special_handling": ["batch_processing", "date_validation"]}, {"name": "favorites", "priority": 5, "description": "User favorites", "estimated_documents": 1000, "special_handling": ["user_reference_validation"]}, {"name": "recycle_bin", "priority": 6, "description": "Soft-deleted documents", "estimated_documents": 500, "special_handling": ["restore_capability"]}], "storage_migration": {"enabled": true, "preserve_folder_structure": true, "verify_file_integrity": true, "update_download_urls": true, "folders_to_migrate": ["documents/", "profile_images/", "temp/"]}, "auth_migration": {"enabled": true, "preserve_user_ids": true, "migrate_custom_claims": true, "send_password_reset": true, "verify_email_addresses": false}, "functions_migration": {"enabled": true, "update_project_references": true, "verify_deployment": true, "functions_to_deploy": ["hybridProcessFileUpload", "getFileAccessUrl", "createCategory", "updateCategory", "deleteCategory", "deleteDocument", "createUser", "logActivity", "validateUserSession", "getActivityStatistics", "healthCheck"]}, "platform_configs": {"android": {"config_file": "android/app/google-services.json", "package_name": "io.document.managementdoc", "update_sha_certificates": true}, "ios": {"config_file": "ios/Runner/GoogleService-Info.plist", "bundle_id": "io.document.managementdoc"}, "web": {"config_file": "web/firebase-config.js", "update_hosting": false}, "flutter": {"config_file": "lib/firebase_options.dart", "generate_new_options": true}}, "verification": {"data_integrity_checks": true, "auth_functionality_test": true, "storage_access_test": true, "functions_health_check": true, "security_rules_validation": true, "performance_baseline": true}, "logging": {"level": "info", "log_file": "./logs/migration-{{timestamp}}.log", "console_output": true, "detailed_errors": true}, "rollback": {"enabled": true, "backup_location": "./backups/migration-{{timestamp}}", "auto_rollback_on_failure": false, "rollback_timeout_minutes": 30}}