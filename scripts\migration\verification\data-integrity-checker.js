#!/usr/bin/env node

/**
 * Data Integrity Checker
 * 
 * Comprehensive data integrity verification system that compares
 * source and target Firebase projects to ensure successful migration.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class DataIntegrityChecker {
  constructor(config) {
    this.config = config;
    this.sourceApp = null;
    this.targetApp = null;
    this.verificationResults = {
      collections: [],
      users: [],
      storage: [],
      summary: {
        total_checks: 0,
        passed: 0,
        failed: 0,
        warnings: 0
      }
    };
  }

  async runIntegrityChecks() {
    console.log('🔍 Running Data Integrity Checks...');
    console.log('==================================\n');
    
    try {
      // Initialize Firebase apps
      await this.initializeFirebaseApps();
      
      // Check Firestore data integrity
      await this.checkFirestoreIntegrity();
      
      // Check Authentication data integrity
      await this.checkAuthIntegrity();
      
      // Check Storage data integrity
      await this.checkStorageIntegrity();
      
      // Generate integrity report
      await this.generateIntegrityReport();
      
      // Evaluate results
      if (this.verificationResults.summary.failed > 0) {
        throw new Error(`Data integrity check failed: ${this.verificationResults.summary.failed} critical issues found`);
      }
      
      console.log('\n✅ All data integrity checks passed!');
      
    } catch (error) {
      console.error('\n❌ Data integrity check failed:', error.message);
      throw error;
    }
  }

  async initializeFirebaseApps() {
    console.log('🔄 Initializing Firebase apps...');
    
    try {
      // Initialize source app
      const sourceServiceAccount = require(path.resolve(__dirname, this.config.source.serviceAccountPath));
      this.sourceApp = admin.initializeApp({
        credential: admin.credential.cert(sourceServiceAccount),
        projectId: this.config.source.projectId,
        storageBucket: this.config.source.storageBucket
      }, 'source-integrity');
      
      // Initialize target app
      const targetServiceAccount = require(path.resolve(__dirname, this.config.target.serviceAccountPath));
      this.targetApp = admin.initializeApp({
        credential: admin.credential.cert(targetServiceAccount),
        projectId: this.config.target.projectId,
        storageBucket: this.config.target.storageBucket
      }, 'target-integrity');
      
      console.log('   ✅ Firebase apps initialized');
      
    } catch (error) {
      throw new Error(`Failed to initialize Firebase apps: ${error.message}`);
    }
  }

  async checkFirestoreIntegrity() {
    console.log('📊 Checking Firestore data integrity...');
    
    const sourceDb = admin.firestore(this.sourceApp);
    const targetDb = admin.firestore(this.targetApp);
    
    for (const collectionConfig of this.config.collections_to_migrate) {
      await this.checkCollectionIntegrity(sourceDb, targetDb, collectionConfig);
    }
  }

  async checkCollectionIntegrity(sourceDb, targetDb, collectionConfig) {
    const collectionName = collectionConfig.name;
    console.log(`   📁 Checking collection: ${collectionName}`);
    
    const result = {
      collection: collectionName,
      checks: [],
      status: 'passed',
      errors: [],
      warnings: []
    };
    
    try {
      // Check 1: Document count
      const countCheck = await this.checkDocumentCount(sourceDb, targetDb, collectionName);
      result.checks.push(countCheck);
      
      // Check 2: Document structure and content
      const contentCheck = await this.checkDocumentContent(sourceDb, targetDb, collectionName, collectionConfig);
      result.checks.push(contentCheck);
      
      // Check 3: Data types and validation
      const typeCheck = await this.checkDataTypes(sourceDb, targetDb, collectionName, collectionConfig);
      result.checks.push(typeCheck);
      
      // Check 4: Relationships and references
      const relationshipCheck = await this.checkRelationships(sourceDb, targetDb, collectionName, collectionConfig);
      result.checks.push(relationshipCheck);
      
      // Evaluate collection result
      const failedChecks = result.checks.filter(check => check.status === 'failed');
      const warningChecks = result.checks.filter(check => check.status === 'warning');
      
      if (failedChecks.length > 0) {
        result.status = 'failed';
        result.errors = failedChecks.map(check => check.error);
        this.verificationResults.summary.failed++;
      } else if (warningChecks.length > 0) {
        result.status = 'warning';
        result.warnings = warningChecks.map(check => check.warning);
        this.verificationResults.summary.warnings++;
      } else {
        this.verificationResults.summary.passed++;
      }
      
      this.verificationResults.summary.total_checks++;
      
      console.log(`   ${this.getStatusIcon(result.status)} Collection ${collectionName}: ${result.status}`);
      
    } catch (error) {
      result.status = 'failed';
      result.errors.push(error.message);
      this.verificationResults.summary.failed++;
      this.verificationResults.summary.total_checks++;
      
      console.log(`   ❌ Collection ${collectionName}: ${error.message}`);
    }
    
    this.verificationResults.collections.push(result);
  }

  async checkDocumentCount(sourceDb, targetDb, collectionName) {
    try {
      const sourceSnapshot = await sourceDb.collection(collectionName).get();
      const targetSnapshot = await targetDb.collection(collectionName).get();
      
      const sourceCount = sourceSnapshot.size;
      const targetCount = targetSnapshot.size;
      
      if (sourceCount !== targetCount) {
        return {
          type: 'document_count',
          status: 'failed',
          error: `Document count mismatch: source=${sourceCount}, target=${targetCount}`
        };
      }
      
      return {
        type: 'document_count',
        status: 'passed',
        result: `${sourceCount} documents verified`
      };
      
    } catch (error) {
      return {
        type: 'document_count',
        status: 'failed',
        error: `Count check failed: ${error.message}`
      };
    }
  }

  async checkDocumentContent(sourceDb, targetDb, collectionName, collectionConfig) {
    try {
      const sourceSnapshot = await sourceDb.collection(collectionName).limit(50).get();
      const sampleSize = Math.min(10, sourceSnapshot.size);
      
      let checkedDocuments = 0;
      let contentMismatches = 0;
      
      for (let i = 0; i < sampleSize; i++) {
        const sourceDoc = sourceSnapshot.docs[i];
        const targetDoc = await targetDb.collection(collectionName).doc(sourceDoc.id).get();
        
        if (!targetDoc.exists) {
          contentMismatches++;
          continue;
        }
        
        const sourceData = sourceDoc.data();
        const targetData = targetDoc.data();
        
        // Remove migration metadata for comparison
        delete targetData._migration;
        
        // Compare essential fields
        const essentialFields = this.getEssentialFields(collectionConfig.name);
        for (const field of essentialFields) {
          if (!this.compareFieldValues(sourceData[field], targetData[field], field)) {
            contentMismatches++;
            break;
          }
        }
        
        checkedDocuments++;
      }
      
      if (contentMismatches > 0) {
        return {
          type: 'document_content',
          status: 'failed',
          error: `Content mismatches found: ${contentMismatches}/${checkedDocuments} documents`
        };
      }
      
      return {
        type: 'document_content',
        status: 'passed',
        result: `${checkedDocuments} documents content verified`
      };
      
    } catch (error) {
      return {
        type: 'document_content',
        status: 'failed',
        error: `Content check failed: ${error.message}`
      };
    }
  }

  async checkDataTypes(sourceDb, targetDb, collectionName, collectionConfig) {
    try {
      const sourceSnapshot = await sourceDb.collection(collectionName).limit(10).get();
      let typeErrors = 0;
      
      for (const sourceDoc of sourceSnapshot.docs) {
        const targetDoc = await targetDb.collection(collectionName).doc(sourceDoc.id).get();
        
        if (!targetDoc.exists) continue;
        
        const sourceData = sourceDoc.data();
        const targetData = targetDoc.data();
        
        // Check data types for essential fields
        const essentialFields = this.getEssentialFields(collectionConfig.name);
        for (const field of essentialFields) {
          if (sourceData[field] !== undefined && targetData[field] !== undefined) {
            if (typeof sourceData[field] !== typeof targetData[field]) {
              typeErrors++;
            }
          }
        }
      }
      
      if (typeErrors > 0) {
        return {
          type: 'data_types',
          status: 'warning',
          warning: `Data type inconsistencies found: ${typeErrors} fields`
        };
      }
      
      return {
        type: 'data_types',
        status: 'passed',
        result: 'Data types consistent'
      };
      
    } catch (error) {
      return {
        type: 'data_types',
        status: 'failed',
        error: `Type check failed: ${error.message}`
      };
    }
  }

  async checkRelationships(sourceDb, targetDb, collectionName, collectionConfig) {
    try {
      // Check user references in documents
      if (collectionName === 'documents') {
        return await this.checkDocumentUserReferences(sourceDb, targetDb);
      }
      
      // Check category references in documents
      if (collectionName === 'documents') {
        return await this.checkDocumentCategoryReferences(sourceDb, targetDb);
      }
      
      // Check user references in activities
      if (collectionName === 'activities') {
        return await this.checkActivityUserReferences(sourceDb, targetDb);
      }
      
      return {
        type: 'relationships',
        status: 'passed',
        result: 'No relationship checks required'
      };
      
    } catch (error) {
      return {
        type: 'relationships',
        status: 'failed',
        error: `Relationship check failed: ${error.message}`
      };
    }
  }

  async checkDocumentUserReferences(sourceDb, targetDb) {
    const documentsSnapshot = await targetDb.collection('documents').limit(20).get();
    let brokenReferences = 0;
    
    for (const doc of documentsSnapshot.docs) {
      const data = doc.data();
      if (data.uploadedBy) {
        const userExists = await targetDb.collection('users').doc(data.uploadedBy).get();
        if (!userExists.exists) {
          brokenReferences++;
        }
      }
    }
    
    if (brokenReferences > 0) {
      return {
        type: 'relationships',
        status: 'failed',
        error: `Broken user references: ${brokenReferences} documents`
      };
    }
    
    return {
      type: 'relationships',
      status: 'passed',
      result: 'User references verified'
    };
  }

  async checkDocumentCategoryReferences(sourceDb, targetDb) {
    const documentsSnapshot = await targetDb.collection('documents').limit(20).get();
    let brokenReferences = 0;
    
    for (const doc of documentsSnapshot.docs) {
      const data = doc.data();
      if (data.category) {
        const categoryExists = await targetDb.collection('categories').doc(data.category).get();
        if (!categoryExists.exists) {
          brokenReferences++;
        }
      }
    }
    
    if (brokenReferences > 0) {
      return {
        type: 'relationships',
        status: 'warning',
        warning: `Broken category references: ${brokenReferences} documents`
      };
    }
    
    return {
      type: 'relationships',
      status: 'passed',
      result: 'Category references verified'
    };
  }

  async checkActivityUserReferences(sourceDb, targetDb) {
    const activitiesSnapshot = await targetDb.collection('activities').limit(20).get();
    let brokenReferences = 0;
    
    for (const doc of activitiesSnapshot.docs) {
      const data = doc.data();
      if (data.userId) {
        const userExists = await targetDb.collection('users').doc(data.userId).get();
        if (!userExists.exists) {
          brokenReferences++;
        }
      }
    }
    
    if (brokenReferences > 0) {
      return {
        type: 'relationships',
        status: 'failed',
        error: `Broken user references: ${brokenReferences} activities`
      };
    }
    
    return {
      type: 'relationships',
      status: 'passed',
      result: 'Activity user references verified'
    };
  }

  async checkAuthIntegrity() {
    console.log('👥 Checking Authentication data integrity...');
    
    const sourceAuth = admin.auth(this.sourceApp);
    const targetAuth = admin.auth(this.targetApp);
    
    const result = {
      component: 'authentication',
      checks: [],
      status: 'passed',
      errors: [],
      warnings: []
    };
    
    try {
      // Check user count
      const sourceUsers = await sourceAuth.listUsers();
      const targetUsers = await targetAuth.listUsers();
      
      if (sourceUsers.users.length !== targetUsers.users.length) {
        result.checks.push({
          type: 'user_count',
          status: 'failed',
          error: `User count mismatch: source=${sourceUsers.users.length}, target=${targetUsers.users.length}`
        });
        result.status = 'failed';
      } else {
        result.checks.push({
          type: 'user_count',
          status: 'passed',
          result: `${sourceUsers.users.length} users verified`
        });
      }
      
      // Sample user data verification
      const sampleSize = Math.min(10, sourceUsers.users.length);
      let userDataErrors = 0;
      
      for (let i = 0; i < sampleSize; i++) {
        const sourceUser = sourceUsers.users[i];
        try {
          const targetUser = await targetAuth.getUser(sourceUser.uid);
          
          if (sourceUser.email !== targetUser.email) {
            userDataErrors++;
          }
        } catch (error) {
          userDataErrors++;
        }
      }
      
      if (userDataErrors > 0) {
        result.checks.push({
          type: 'user_data',
          status: 'failed',
          error: `User data errors: ${userDataErrors}/${sampleSize} users`
        });
        result.status = 'failed';
      } else {
        result.checks.push({
          type: 'user_data',
          status: 'passed',
          result: `${sampleSize} users data verified`
        });
      }
      
      console.log(`   ${this.getStatusIcon(result.status)} Authentication: ${result.status}`);
      
    } catch (error) {
      result.status = 'failed';
      result.errors.push(error.message);
      console.log(`   ❌ Authentication: ${error.message}`);
    }
    
    this.verificationResults.users.push(result);
    this.updateSummary(result.status);
  }

  async checkStorageIntegrity() {
    console.log('📁 Checking Storage data integrity...');
    
    const sourceBucket = admin.storage(this.sourceApp).bucket();
    const targetBucket = admin.storage(this.targetApp).bucket();
    
    for (const folder of this.config.storage_migration.folders_to_migrate) {
      await this.checkStorageFolder(sourceBucket, targetBucket, folder);
    }
  }

  async checkStorageFolder(sourceBucket, targetBucket, folder) {
    const result = {
      folder: folder,
      checks: [],
      status: 'passed',
      errors: [],
      warnings: []
    };
    
    try {
      const [sourceFiles] = await sourceBucket.getFiles({ prefix: folder });
      const [targetFiles] = await targetBucket.getFiles({ prefix: folder });
      
      // Check file count
      if (sourceFiles.length !== targetFiles.length) {
        result.checks.push({
          type: 'file_count',
          status: 'failed',
          error: `File count mismatch: source=${sourceFiles.length}, target=${targetFiles.length}`
        });
        result.status = 'failed';
      } else {
        result.checks.push({
          type: 'file_count',
          status: 'passed',
          result: `${sourceFiles.length} files verified`
        });
      }
      
      // Sample file verification
      const sampleSize = Math.min(5, sourceFiles.length);
      let fileErrors = 0;
      
      for (let i = 0; i < sampleSize; i++) {
        const sourceFile = sourceFiles[i];
        const targetFile = targetFiles.find(f => f.name === sourceFile.name);
        
        if (!targetFile) {
          fileErrors++;
          continue;
        }
        
        const [sourceMetadata] = await sourceFile.getMetadata();
        const [targetMetadata] = await targetFile.getMetadata();
        
        if (sourceMetadata.size !== targetMetadata.size) {
          fileErrors++;
        }
      }
      
      if (fileErrors > 0) {
        result.checks.push({
          type: 'file_integrity',
          status: 'failed',
          error: `File integrity errors: ${fileErrors}/${sampleSize} files`
        });
        result.status = 'failed';
      } else {
        result.checks.push({
          type: 'file_integrity',
          status: 'passed',
          result: `${sampleSize} files integrity verified`
        });
      }
      
      console.log(`   ${this.getStatusIcon(result.status)} Storage ${folder}: ${result.status}`);
      
    } catch (error) {
      result.status = 'failed';
      result.errors.push(error.message);
      console.log(`   ❌ Storage ${folder}: ${error.message}`);
    }
    
    this.verificationResults.storage.push(result);
    this.updateSummary(result.status);
  }

  getEssentialFields(collectionName) {
    const fieldMap = {
      'users': ['id', 'email', 'fullName', 'role', 'status'],
      'documents': ['id', 'fileName', 'uploadedBy', 'category'],
      'categories': ['id', 'name'],
      'activities': ['type', 'userId'],
      'favorites': ['userId', 'folderPath'],
      'recycle_bin': ['originalDocumentId', 'deletedBy']
    };
    
    return fieldMap[collectionName] || ['id'];
  }

  compareFieldValues(sourceValue, targetValue, fieldName) {
    // Handle null/undefined values
    if (sourceValue === null || sourceValue === undefined) {
      return targetValue === null || targetValue === undefined;
    }
    
    // Handle timestamp fields
    if (this.isTimestampField(fieldName)) {
      return this.compareTimestamps(sourceValue, targetValue);
    }
    
    // Handle arrays
    if (Array.isArray(sourceValue)) {
      if (!Array.isArray(targetValue)) return false;
      if (sourceValue.length !== targetValue.length) return false;
      return sourceValue.every((item, index) => item === targetValue[index]);
    }
    
    // Handle objects
    if (typeof sourceValue === 'object' && sourceValue !== null) {
      if (typeof targetValue !== 'object' || targetValue === null) return false;
      return JSON.stringify(sourceValue) === JSON.stringify(targetValue);
    }
    
    // Handle primitive values
    return sourceValue === targetValue;
  }

  isTimestampField(fieldName) {
    const timestampFields = ['createdAt', 'updatedAt', 'uploadedAt', 'deletedAt', 'timestamp'];
    return timestampFields.includes(fieldName);
  }

  compareTimestamps(source, target) {
    if (!source || !target) return source === target;
    
    const sourceTime = source.toDate ? source.toDate().getTime() : new Date(source).getTime();
    const targetTime = target.toDate ? target.toDate().getTime() : new Date(target).getTime();
    
    return Math.abs(sourceTime - targetTime) < 1000; // 1 second tolerance
  }

  getStatusIcon(status) {
    const icons = {
      'passed': '✅',
      'failed': '❌',
      'warning': '⚠️'
    };
    return icons[status] || '❓';
  }

  updateSummary(status) {
    this.verificationResults.summary.total_checks++;
    
    switch (status) {
      case 'passed':
        this.verificationResults.summary.passed++;
        break;
      case 'failed':
        this.verificationResults.summary.failed++;
        break;
      case 'warning':
        this.verificationResults.summary.warnings++;
        break;
    }
  }

  async generateIntegrityReport() {
    const report = {
      migration_id: this.config.migration.id,
      integrity_check_timestamp: new Date().toISOString(),
      summary: this.verificationResults.summary,
      results: {
        collections: this.verificationResults.collections,
        users: this.verificationResults.users,
        storage: this.verificationResults.storage
      }
    };
    
    const reportPath = path.join(__dirname, '../logs', `data-integrity-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Data Integrity Report:');
    console.log(`   Total Checks: ${report.summary.total_checks}`);
    console.log(`   Passed: ${report.summary.passed}`);
    console.log(`   Failed: ${report.summary.failed}`);
    console.log(`   Warnings: ${report.summary.warnings}`);
    console.log(`   Report saved: ${reportPath}`);
  }
}

// Run integrity checks if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found. Run setup first: node config/setup-migration-config.js');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  const checker = new DataIntegrityChecker(config);
  
  checker.runIntegrityChecks()
    .then(() => {
      console.log('\n🎉 Data integrity checks completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Data integrity checks failed:', error.message);
      process.exit(1);
    });
}

module.exports = DataIntegrityChecker;
