#!/usr/bin/env node

/**
 * Firebase Migration Orchestrator
 * 
 * Main orchestration script that coordinates the entire Firebase migration process
 * with proper error handling, logging, and verification steps.
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Import migration modules
const BackupSystem = require('../data/backup-system');
const FirestoreMigration = require('../data/firestore-migration');
const AuthMigration = require('../data/auth-migration');
const StorageMigration = require('../data/storage-migration');
const FlutterConfigUpdater = require('../config/flutter-config-updater');
const PreMigrationChecks = require('./pre-migration-checks');
const PostMigrationChecks = require('./post-migration-checks');

class MigrationOrchestrator {
  constructor(configPath) {
    this.configPath = configPath;
    this.config = null;
    this.migrationState = {
      phase: 'not_started',
      startTime: null,
      endTime: null,
      completedSteps: [],
      failedSteps: [],
      backupPath: null
    };
    
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async initialize() {
    console.log('🔄 Initializing Migration Orchestrator...');
    
    // Load configuration
    if (!fs.existsSync(this.configPath)) {
      throw new Error('Migration configuration not found. Run setup first.');
    }
    
    this.config = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
    
    // Create logs directory
    const logsDir = path.join(__dirname, '../logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    console.log('✅ Migration Orchestrator initialized');
    console.log(`📋 Migration ID: ${this.config.migration.id}`);
    console.log(`🎯 Target Project: ${this.config.target.projectId}`);
  }

  async startMigration() {
    console.log('\n🚀 Starting Firebase Project Migration');
    console.log('=====================================\n');
    
    try {
      this.migrationState.startTime = new Date();
      this.migrationState.phase = 'in_progress';
      
      // Display migration overview
      await this.displayMigrationOverview();
      
      // Confirm migration start
      const confirmed = await this.confirmMigrationStart();
      if (!confirmed) {
        console.log('❌ Migration cancelled by user');
        return;
      }
      
      // Phase 1: Pre-migration checks
      await this.executePhase('pre_checks', 'Pre-Migration Checks', async () => {
        const preChecks = new PreMigrationChecks(this.config);
        await preChecks.runAllChecks();
      });
      
      // Phase 2: Create backup
      if (this.config.migration_settings.backup_before_migration) {
        await this.executePhase('backup', 'Create Backup', async () => {
          const backup = new BackupSystem(this.config);
          await backup.initialize();
          this.migrationState.backupPath = await backup.createFullBackup();
        });
      }
      
      // Phase 3: Migrate Firestore data
      await this.executePhase('firestore', 'Migrate Firestore Data', async () => {
        const firestoreMigration = new FirestoreMigration(this.config);
        await firestoreMigration.initialize();
        await firestoreMigration.migrateAllCollections();
      });
      
      // Phase 4: Migrate Authentication users
      if (this.config.auth_migration.enabled) {
        await this.executePhase('auth', 'Migrate Authentication Users', async () => {
          const authMigration = new AuthMigration(this.config);
          await authMigration.initialize();
          await authMigration.migrateAllUsers();
        });
      }
      
      // Phase 5: Migrate Storage files
      if (this.config.storage_migration.enabled) {
        await this.executePhase('storage', 'Migrate Storage Files', async () => {
          const storageMigration = new StorageMigration(this.config);
          await storageMigration.initialize();
          await storageMigration.migrateAllFiles();
        });
      }
      
      // Phase 6: Deploy security rules and functions
      await this.executePhase('rules_functions', 'Deploy Rules and Functions', async () => {
        await this.deploySecurityRules();
        if (this.config.functions_migration.enabled) {
          await this.deployFunctions();
        }
      });
      
      // Phase 7: Update app configurations
      await this.executePhase('config_update', 'Update App Configurations', async () => {
        const configUpdater = new FlutterConfigUpdater(this.config);
        await configUpdater.updateAllConfigurations();
      });
      
      // Phase 8: Post-migration verification
      if (this.config.verification.data_integrity_checks) {
        await this.executePhase('verification', 'Post-Migration Verification', async () => {
          const postChecks = new PostMigrationChecks(this.config);
          await postChecks.runAllVerifications();
        });
      }
      
      this.migrationState.endTime = new Date();
      this.migrationState.phase = 'completed';
      
      await this.generateFinalReport();
      
      console.log('\n🎉 Migration completed successfully!');
      console.log('=====================================');
      
    } catch (error) {
      this.migrationState.endTime = new Date();
      this.migrationState.phase = 'failed';
      
      await this.handleMigrationFailure(error);
      throw error;
      
    } finally {
      this.rl.close();
    }
  }

  async executePhase(phaseId, phaseName, phaseFunction) {
    console.log(`\n📋 Phase: ${phaseName}`);
    console.log('─'.repeat(50));
    
    const startTime = new Date();
    
    try {
      await phaseFunction();
      
      const endTime = new Date();
      const duration = endTime - startTime;
      
      this.migrationState.completedSteps.push({
        phase: phaseId,
        name: phaseName,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration: duration,
        status: 'completed'
      });
      
      console.log(`✅ ${phaseName} completed in ${this.formatDuration(duration)}`);
      
    } catch (error) {
      const endTime = new Date();
      const duration = endTime - startTime;
      
      this.migrationState.failedSteps.push({
        phase: phaseId,
        name: phaseName,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration: duration,
        status: 'failed',
        error: error.message
      });
      
      console.error(`❌ ${phaseName} failed: ${error.message}`);
      throw error;
    }
  }

  async displayMigrationOverview() {
    console.log('📊 Migration Overview:');
    console.log(`   Source Project: ${this.config.source.projectId}`);
    console.log(`   Target Project: ${this.config.target.projectId}`);
    console.log(`   Collections to migrate: ${this.config.collections_to_migrate.length}`);
    console.log(`   Auth migration: ${this.config.auth_migration.enabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   Storage migration: ${this.config.storage_migration.enabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   Functions migration: ${this.config.functions_migration.enabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   Backup enabled: ${this.config.migration_settings.backup_before_migration ? 'Yes' : 'No'}`);
    console.log(`   Verification enabled: ${this.config.verification.data_integrity_checks ? 'Yes' : 'No'}`);
  }

  async confirmMigrationStart() {
    console.log('\n⚠️  IMPORTANT WARNINGS:');
    console.log('   • This migration will modify your target Firebase project');
    console.log('   • Ensure you have proper backups before proceeding');
    console.log('   • The migration process may take several hours depending on data size');
    console.log('   • Do not interrupt the migration process once started');
    
    const answer = await this.prompt('\n🤔 Do you want to proceed with the migration? (yes/no): ');
    return answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y';
  }

  async deploySecurityRules() {
    console.log('   🔒 Deploying Firestore security rules...');
    
    try {
      const { execSync } = require('child_process');
      
      // Deploy Firestore rules
      execSync('firebase deploy --only firestore:rules', {
        cwd: path.resolve(__dirname, '../../../..'),
        stdio: 'pipe'
      });
      
      // Deploy Firestore indexes
      execSync('firebase deploy --only firestore:indexes', {
        cwd: path.resolve(__dirname, '../../../..'),
        stdio: 'pipe'
      });
      
      // Deploy Storage rules
      execSync('firebase deploy --only storage', {
        cwd: path.resolve(__dirname, '../../../..'),
        stdio: 'pipe'
      });
      
      console.log('   ✅ Security rules deployed successfully');
      
    } catch (error) {
      throw new Error(`Failed to deploy security rules: ${error.message}`);
    }
  }

  async deployFunctions() {
    console.log('   ⚡ Deploying Firebase Functions...');
    
    try {
      const { execSync } = require('child_process');
      
      // Update functions configuration for new project
      await this.updateFunctionsConfig();
      
      // Deploy functions
      execSync('firebase deploy --only functions', {
        cwd: path.resolve(__dirname, '../../../..'),
        stdio: 'pipe'
      });
      
      console.log('   ✅ Functions deployed successfully');
      
    } catch (error) {
      throw new Error(`Failed to deploy functions: ${error.message}`);
    }
  }

  async updateFunctionsConfig() {
    const functionsIndexPath = path.resolve(__dirname, '../../../../functions/src/index.ts');
    
    if (fs.existsSync(functionsIndexPath)) {
      let content = fs.readFileSync(functionsIndexPath, 'utf8');
      
      // Update project ID references
      content = content.replace(
        new RegExp(this.config.source.projectId, 'g'),
        this.config.target.projectId
      );
      
      // Update storage bucket references
      content = content.replace(
        new RegExp(this.config.source.storageBucket, 'g'),
        this.config.target.storageBucket
      );
      
      fs.writeFileSync(functionsIndexPath, content);
      console.log('   ✅ Functions configuration updated');
    }
  }

  async handleMigrationFailure(error) {
    console.error('\n❌ Migration Failed!');
    console.error('==================');
    console.error(`Error: ${error.message}`);
    
    if (this.config.rollback.enabled && this.config.rollback.auto_rollback_on_failure) {
      console.log('\n🔄 Attempting automatic rollback...');
      
      try {
        const RollbackSystem = require('./rollback-system');
        const rollback = new RollbackSystem(this.config, this.migrationState.backupPath);
        await rollback.performRollback();
        
        console.log('✅ Rollback completed successfully');
        
      } catch (rollbackError) {
        console.error('❌ Rollback failed:', rollbackError.message);
        console.log('📝 Manual intervention required');
      }
    }
    
    await this.generateFailureReport(error);
  }

  async generateFinalReport() {
    const totalDuration = this.migrationState.endTime - this.migrationState.startTime;
    
    const report = {
      migration_id: this.config.migration.id,
      source_project: this.config.source.projectId,
      target_project: this.config.target.projectId,
      status: this.migrationState.phase,
      start_time: this.migrationState.startTime.toISOString(),
      end_time: this.migrationState.endTime.toISOString(),
      total_duration: totalDuration,
      total_duration_formatted: this.formatDuration(totalDuration),
      completed_steps: this.migrationState.completedSteps,
      failed_steps: this.migrationState.failedSteps,
      backup_path: this.migrationState.backupPath,
      summary: {
        total_phases: this.migrationState.completedSteps.length + this.migrationState.failedSteps.length,
        completed_phases: this.migrationState.completedSteps.length,
        failed_phases: this.migrationState.failedSteps.length,
        success_rate: this.migrationState.completedSteps.length / 
          (this.migrationState.completedSteps.length + this.migrationState.failedSteps.length) * 100
      }
    };
    
    const reportPath = path.join(__dirname, '../logs', `migration-final-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Final Migration Report:');
    console.log(`   Status: ${report.status}`);
    console.log(`   Total Duration: ${report.total_duration_formatted}`);
    console.log(`   Completed Phases: ${report.summary.completed_phases}/${report.summary.total_phases}`);
    console.log(`   Success Rate: ${report.summary.success_rate.toFixed(1)}%`);
    console.log(`   Report saved: ${reportPath}`);
    
    if (this.migrationState.backupPath) {
      console.log(`   Backup location: ${this.migrationState.backupPath}`);
    }
  }

  async generateFailureReport(error) {
    const report = {
      migration_id: this.config.migration.id,
      status: 'failed',
      error: error.message,
      completed_steps: this.migrationState.completedSteps,
      failed_steps: this.migrationState.failedSteps,
      backup_path: this.migrationState.backupPath,
      timestamp: new Date().toISOString()
    };
    
    const reportPath = path.join(__dirname, '../logs', `migration-failure-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`📄 Failure report saved: ${reportPath}`);
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  async prompt(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }
}

// Run migration if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  const orchestrator = new MigrationOrchestrator(configPath);
  
  orchestrator.initialize()
    .then(() => orchestrator.startMigration())
    .then(() => {
      console.log('\n🎉 Migration orchestration completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Migration orchestration failed:', error.message);
      process.exit(1);
    });
}

module.exports = MigrationOrchestrator;
