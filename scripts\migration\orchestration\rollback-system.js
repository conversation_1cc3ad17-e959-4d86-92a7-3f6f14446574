#!/usr/bin/env node

/**
 * Rollback System
 * 
 * Provides rollback capabilities in case of migration failure
 * by restoring data from backups and reverting configurations.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

class RollbackSystem {
  constructor(config, backupPath) {
    this.config = config;
    this.backupPath = backupPath;
    this.rollbackStats = {
      collections: 0,
      documents: 0,
      users: 0,
      files: 0,
      startTime: null,
      endTime: null
    };
  }

  async performRollback() {
    console.log('🔄 Starting rollback process...');
    console.log('==============================\n');
    
    if (!this.backupPath || !fs.existsSync(this.backupPath)) {
      throw new Error('Backup not found. Cannot perform rollback.');
    }
    
    try {
      this.rollbackStats.startTime = new Date();
      
      // Initialize target Firebase app
      await this.initializeTargetApp();
      
      // Load backup manifest
      const manifest = await this.loadBackupManifest();
      
      // Rollback Firestore data
      if (manifest.components.includes('firestore')) {
        await this.rollbackFirestore();
      }
      
      // Rollback Authentication users
      if (manifest.components.includes('auth')) {
        await this.rollbackAuth();
      }
      
      // Rollback Storage files
      if (manifest.components.includes('storage')) {
        await this.rollbackStorage();
      }
      
      // Rollback configurations
      await this.rollbackConfigurations();
      
      this.rollbackStats.endTime = new Date();
      
      await this.generateRollbackReport();
      
      console.log('\n✅ Rollback completed successfully!');
      
    } catch (error) {
      this.rollbackStats.endTime = new Date();
      await this.generateRollbackReport();
      throw error;
    }
  }

  async initializeTargetApp() {
    console.log('🔄 Initializing target app for rollback...');
    
    try {
      const serviceAccount = require(path.resolve(__dirname, this.config.target.serviceAccountPath));
      this.targetApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: this.config.target.projectId,
        storageBucket: this.config.target.storageBucket
      }, 'target-rollback');
      
      console.log('   ✅ Target app initialized');
      
    } catch (error) {
      throw new Error(`Failed to initialize target app: ${error.message}`);
    }
  }

  async loadBackupManifest() {
    const manifestPath = path.join(this.backupPath, 'backup-manifest.json');
    
    if (!fs.existsSync(manifestPath)) {
      throw new Error('Backup manifest not found');
    }
    
    return JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  }

  async rollbackFirestore() {
    console.log('📊 Rolling back Firestore data...');
    
    const db = admin.firestore(this.targetApp);
    const firestorePath = path.join(this.backupPath, 'firestore');
    
    if (!fs.existsSync(firestorePath)) {
      console.log('   ⚠️  No Firestore backup found, skipping...');
      return;
    }
    
    // Clear existing data first
    await this.clearFirestoreData(db);
    
    // Restore from backup
    const backupFiles = fs.readdirSync(firestorePath);
    
    for (const backupFile of backupFiles) {
      if (!backupFile.endsWith('.json')) continue;
      
      const collectionName = path.basename(backupFile, '.json');
      console.log(`   Restoring collection: ${collectionName}`);
      
      const backupData = JSON.parse(fs.readFileSync(path.join(firestorePath, backupFile), 'utf8'));
      
      // Restore documents in batches
      const batchSize = 500;
      for (let i = 0; i < backupData.length; i += batchSize) {
        const batch = db.batch();
        const batchData = backupData.slice(i, i + batchSize);
        
        for (const docData of batchData) {
          const docRef = db.collection(collectionName).doc(docData.id);
          batch.set(docRef, docData.data);
        }
        
        await batch.commit();
        this.rollbackStats.documents += batchData.length;
      }
      
      this.rollbackStats.collections++;
      console.log(`   ✅ Collection ${collectionName} restored: ${backupData.length} documents`);
    }
  }

  async clearFirestoreData(db) {
    console.log('   🗑️  Clearing existing Firestore data...');
    
    const collections = await db.listCollections();
    
    for (const collection of collections) {
      // Skip system collections
      if (collection.id.startsWith('_')) continue;
      
      const snapshot = await collection.get();
      const batch = db.batch();
      
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      
      if (!snapshot.empty) {
        await batch.commit();
      }
    }
  }

  async rollbackAuth() {
    console.log('👥 Rolling back Authentication users...');
    
    const auth = admin.auth(this.targetApp);
    const authPath = path.join(this.backupPath, 'auth');
    
    if (!fs.existsSync(authPath)) {
      console.log('   ⚠️  No Auth backup found, skipping...');
      return;
    }
    
    // Clear existing users first
    await this.clearAuthUsers(auth);
    
    // Restore from backup
    const usersBackupPath = path.join(authPath, 'users.json');
    if (fs.existsSync(usersBackupPath)) {
      const backupUsers = JSON.parse(fs.readFileSync(usersBackupPath, 'utf8'));
      
      // Import users in batches
      const batchSize = 1000;
      for (let i = 0; i < backupUsers.length; i += batchSize) {
        const batchUsers = backupUsers.slice(i, i + batchSize);
        
        try {
          await auth.importUsers(batchUsers);
          this.rollbackStats.users += batchUsers.length;
        } catch (error) {
          console.warn(`   ⚠️  Failed to import user batch: ${error.message}`);
        }
      }
      
      console.log(`   ✅ Authentication restored: ${backupUsers.length} users`);
    }
  }

  async clearAuthUsers(auth) {
    console.log('   🗑️  Clearing existing Auth users...');
    
    let nextPageToken;
    
    do {
      const listUsersResult = await auth.listUsers(1000, nextPageToken);
      
      for (const user of listUsersResult.users) {
        try {
          await auth.deleteUser(user.uid);
        } catch (error) {
          console.warn(`   ⚠️  Failed to delete user ${user.uid}: ${error.message}`);
        }
      }
      
      nextPageToken = listUsersResult.pageToken;
      
    } while (nextPageToken);
  }

  async rollbackStorage() {
    console.log('📁 Rolling back Storage files...');
    
    const bucket = admin.storage(this.targetApp).bucket();
    const storagePath = path.join(this.backupPath, 'storage');
    
    if (!fs.existsSync(storagePath)) {
      console.log('   ⚠️  No Storage backup found, skipping...');
      return;
    }
    
    // Clear existing files first
    await this.clearStorageFiles(bucket);
    
    // Note: This is a simplified rollback that only restores file metadata
    // In a full implementation, you would also restore the actual file contents
    const manifestPath = path.join(storagePath, 'files-manifest.json');
    if (fs.existsSync(manifestPath)) {
      const filesManifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      
      console.log(`   📋 Storage manifest loaded: ${filesManifest.length} files`);
      console.log('   ⚠️  Note: File contents not restored in this rollback');
      console.log('   📝 Manual file restoration may be required');
      
      this.rollbackStats.files = filesManifest.length;
    }
  }

  async clearStorageFiles(bucket) {
    console.log('   🗑️  Clearing existing Storage files...');
    
    try {
      const [files] = await bucket.getFiles();
      
      for (const file of files) {
        try {
          await file.delete();
        } catch (error) {
          console.warn(`   ⚠️  Failed to delete file ${file.name}: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.warn(`   ⚠️  Failed to clear storage: ${error.message}`);
    }
  }

  async rollbackConfigurations() {
    console.log('⚙️  Rolling back configurations...');
    
    try {
      // Restore .firebaserc
      await this.restoreFirebaseRC();
      
      // Restore firebase.json
      await this.restoreFirebaseJSON();
      
      // Restore Flutter configurations
      await this.restoreFlutterConfigs();
      
      console.log('   ✅ Configurations rolled back');
      
    } catch (error) {
      console.warn(`   ⚠️  Configuration rollback failed: ${error.message}`);
    }
  }

  async restoreFirebaseRC() {
    const firebaseRCPath = path.resolve(__dirname, '../../../..', '.firebaserc');
    const backupPath = firebaseRCPath + '.backup-' + this.getLatestBackupSuffix();
    
    if (fs.existsSync(backupPath)) {
      fs.copyFileSync(backupPath, firebaseRCPath);
      console.log('   ✅ .firebaserc restored');
    }
  }

  async restoreFirebaseJSON() {
    const firebaseJSONPath = path.resolve(__dirname, '../../../..', 'firebase.json');
    const backupPath = firebaseJSONPath + '.backup-' + this.getLatestBackupSuffix();
    
    if (fs.existsSync(backupPath)) {
      fs.copyFileSync(backupPath, firebaseJSONPath);
      console.log('   ✅ firebase.json restored');
    }
  }

  async restoreFlutterConfigs() {
    const configPaths = [
      'lib/firebase_options.dart',
      'android/app/google-services.json',
      'ios/Runner/GoogleService-Info.plist',
      'lib/config/firebase_config.dart'
    ];
    
    for (const configPath of configPaths) {
      const fullPath = path.resolve(__dirname, '../../../..', configPath);
      const backupPath = fullPath + '.backup-' + this.getLatestBackupSuffix();
      
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, fullPath);
        console.log(`   ✅ ${configPath} restored`);
      }
    }
  }

  getLatestBackupSuffix() {
    // This would find the latest backup suffix in a real implementation
    // For now, we'll use a placeholder
    return Date.now();
  }

  async generateRollbackReport() {
    const duration = this.rollbackStats.endTime - this.rollbackStats.startTime;
    
    const report = {
      migration_id: this.config.migration.id,
      rollback_timestamp: new Date().toISOString(),
      backup_path: this.backupPath,
      duration_ms: duration,
      duration_formatted: this.formatDuration(duration),
      statistics: this.rollbackStats,
      status: this.rollbackStats.endTime ? 'completed' : 'failed'
    };
    
    const reportPath = path.join(__dirname, '../logs', `rollback-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Rollback Report:');
    console.log(`   Status: ${report.status}`);
    console.log(`   Collections: ${report.statistics.collections}`);
    console.log(`   Documents: ${report.statistics.documents}`);
    console.log(`   Users: ${report.statistics.users}`);
    console.log(`   Files: ${report.statistics.files}`);
    console.log(`   Duration: ${report.duration_formatted}`);
    console.log(`   Report saved: ${reportPath}`);
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}

// Run rollback if called directly
if (require.main === module) {
  const configPath = path.join(__dirname, '../config/migration-config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Migration config not found');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  
  // Get backup path from command line argument or use latest
  const backupPath = process.argv[2] || config.rollback.backup_location;
  
  if (!backupPath) {
    console.error('❌ Backup path not specified');
    console.log('Usage: node rollback-system.js <backup-path>');
    process.exit(1);
  }
  
  const rollback = new RollbackSystem(config, backupPath);
  
  rollback.performRollback()
    .then(() => {
      console.log('\n🎉 Rollback completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Rollback failed:', error.message);
      process.exit(1);
    });
}

module.exports = RollbackSystem;
