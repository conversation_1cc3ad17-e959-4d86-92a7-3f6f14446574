# 🔄 Firebase Project Migration Guide

## Overview

This comprehensive guide walks you through the complete Firebase project migration process, from preparation to post-migration verification. The migration system is designed to transfer your entire Firebase project with minimal downtime and zero data loss.

## 📋 Prerequisites

### System Requirements
- **Node.js**: Version 18 or higher
- **Firebase CLI**: Latest version (`npm install -g firebase-tools`)
- **Git**: For version control and backup
- **Minimum 8GB RAM**: For handling large data migrations
- **Stable Internet**: High-speed connection recommended

### Firebase Requirements
- **Source Project**: Admin access to current Firebase project
- **Target Project**: New Firebase project with billing enabled
- **Service Account Keys**: Downloaded for both projects
- **Authentication**: Firebase CLI authenticated with both projects

### Preparation Checklist
- [ ] Create new Firebase project in Firebase Console
- [ ] Enable billing on target project
- [ ] Download service account keys for both projects
- [ ] Backup current project data
- [ ] Notify users of planned maintenance window
- [ ] Test migration process in development environment

## 🚀 Migration Process

### Phase 1: Setup and Configuration

#### Step 1: Initialize Migration System
```bash
cd scripts/migration
node config/setup-migration-config.js
```

This will:
- Prompt for target project details
- Validate access to both projects
- Generate configuration files
- Create necessary directories

#### Step 2: Configure Migration Settings
Edit `config/migration-config.json` to customize:
- Batch sizes for data migration
- Collections to migrate
- Migration priorities
- Backup and rollback settings

#### Step 3: Generate Platform Configurations
```bash
node config/platform-config-generator.js
```

This creates template configuration files for:
- Android (`google-services.json`)
- iOS (`GoogleService-Info.plist`)
- Web (`firebase-config.js`)
- Windows (`FirebaseConfig.cs`)

### Phase 2: Pre-Migration Validation

#### Step 4: Run Pre-Migration Checks
```bash
node orchestration/pre-migration-checks.js
```

This validates:
- System requirements
- Firebase CLI setup
- Project access permissions
- Data size estimation
- Capacity limits

**Important**: Fix all critical issues before proceeding.

### Phase 3: Data Migration

#### Step 5: Create Backup (Recommended)
```bash
node data/backup-system.js
```

Creates comprehensive backup including:
- All Firestore collections
- Authentication users
- Storage files metadata
- Security rules and indexes
- Functions source code

#### Step 6: Execute Migration
```bash
node orchestration/migration-orchestrator.js
```

This orchestrates the complete migration:
1. **Firestore Data**: Migrates all collections with special handling
2. **Authentication**: Transfers users with preserved UIDs
3. **Storage**: Copies files with integrity verification
4. **Security Rules**: Deploys rules and indexes
5. **Functions**: Updates and deploys functions
6. **Configuration**: Updates app configurations

**Migration Progress**: The orchestrator provides real-time progress updates and detailed logging.

### Phase 4: Post-Migration Verification

#### Step 7: Verify Data Integrity
```bash
node verification/data-integrity-checker.js
```

Performs comprehensive verification:
- Document count validation
- Content integrity checks
- User data verification
- Storage file validation
- Relationship consistency

#### Step 8: Run Post-Migration Checks
```bash
node orchestration/post-migration-checks.js
```

Validates:
- System functionality
- Performance baselines
- Security rules operation
- Functions deployment
- End-to-end workflows

### Phase 5: App Configuration Update

#### Step 9: Update Flutter App Configuration
```bash
node config/flutter-config-updater.js
```

Updates:
- `firebase_options.dart`
- Platform-specific config files
- Project references in code
- `.firebaserc` and `firebase.json`

#### Step 10: Manual Configuration Steps
1. **Download actual configuration files** from Firebase Console
2. **Replace template files** with downloaded versions
3. **Update SHA-1 certificates** for Android
4. **Verify bundle IDs** for iOS
5. **Test app builds** on all platforms

## 🔧 Configuration Details

### Source Project Configuration
```json
{
  "projectId": "document-management-c5a96",
  "projectNumber": "865405248457",
  "storageBucket": "document-management-c5a96.firebasestorage.app"
}
```

### Target Project Configuration
```json
{
  "projectId": "your-new-project-id",
  "projectNumber": "your-project-number",
  "storageBucket": "your-new-project-id.firebasestorage.app"
}
```

### Collections Migration Priority
1. **users** (Priority 1): User profiles and permissions
2. **categories** (Priority 2): Document categories
3. **documents** (Priority 3): Document metadata
4. **activities** (Priority 4): User activity logs
5. **favorites** (Priority 5): User favorites
6. **recycle_bin** (Priority 6): Soft-deleted documents

### Special Handling Features
- **User ID Preservation**: Maintains authentication consistency
- **Storage Reference Updates**: Updates file URLs to new project
- **Relationship Validation**: Ensures data consistency
- **Migration Metadata**: Tracks migration history
- **Timestamp Preservation**: Maintains original timestamps

## ⚠️ Important Considerations

### Downtime Planning
- **Estimated Downtime**: 2-6 hours depending on data size
- **User Notification**: Inform users 48 hours in advance
- **Maintenance Window**: Schedule during low-usage periods
- **Rollback Plan**: Have rollback procedure ready

### Data Size Impact
- **Small Projects** (<1GB): 1-2 hours
- **Medium Projects** (1-10GB): 2-4 hours
- **Large Projects** (>10GB): 4-8 hours

### Rate Limiting
- **Firestore**: 10,000 writes/second limit
- **Authentication**: 1,000 operations/minute limit
- **Storage**: 5,000 operations/second limit

### Security Considerations
- **Service Account Keys**: Keep secure and rotate after migration
- **Network Security**: Use secure connections only
- **Access Control**: Limit migration access to essential personnel
- **Audit Trail**: Maintain detailed logs of all operations

## 🚨 Troubleshooting

### Common Issues

#### Migration Fails During Firestore Transfer
**Symptoms**: Error messages about write limits or timeouts
**Solutions**:
- Reduce batch size in configuration
- Increase retry delays
- Check Firestore quotas in target project

#### Authentication Users Not Migrated
**Symptoms**: User count mismatch after migration
**Solutions**:
- Verify service account permissions
- Check user import limits
- Review authentication configuration

#### Storage Files Missing
**Symptoms**: File count mismatch or broken download URLs
**Solutions**:
- Verify storage bucket permissions
- Check file size limits
- Review storage rules deployment

#### Functions Deployment Fails
**Symptoms**: Functions not appearing in target project
**Solutions**:
- Verify billing is enabled
- Check function dependencies
- Review project references in code

### Error Recovery

#### Partial Migration Failure
1. **Review error logs** in `logs/` directory
2. **Identify failed components** from migration report
3. **Re-run specific migration scripts** for failed components
4. **Verify data integrity** after partial recovery

#### Complete Migration Failure
1. **Stop migration process** immediately
2. **Review failure report** for root cause
3. **Execute rollback procedure** if needed
4. **Fix underlying issues** before retry

### Rollback Procedure
```bash
# Emergency rollback
node orchestration/rollback-system.js /path/to/backup

# Restore configurations
git checkout -- .firebaserc firebase.json
git checkout -- lib/firebase_options.dart
git checkout -- android/app/google-services.json
```

## 📞 Support and Resources

### Documentation
- `TROUBLESHOOTING.md`: Detailed troubleshooting guide
- `VERIFICATION_CHECKLIST.md`: Post-migration verification steps
- `CONFIGURATION_INSTRUCTIONS.md`: Platform configuration details

### Log Files
- `logs/migration-final-report-*.json`: Complete migration summary
- `logs/firestore-migration-*.json`: Firestore migration details
- `logs/auth-migration-*.json`: Authentication migration details
- `logs/storage-migration-*.json`: Storage migration details

### Emergency Contacts
- **Technical Lead**: [Your contact information]
- **Firebase Support**: [Firebase support channels]
- **Project Manager**: [PM contact information]

## ✅ Success Criteria

### Migration Completion Checklist
- [ ] All collections migrated with correct document counts
- [ ] All users migrated with preserved authentication
- [ ] All storage files transferred with integrity verification
- [ ] Security rules and indexes deployed successfully
- [ ] Functions deployed and operational
- [ ] App configurations updated and tested
- [ ] Data integrity verification passed
- [ ] Performance baselines established
- [ ] User acceptance testing completed

### Post-Migration Validation
- [ ] Users can log in successfully
- [ ] File uploads and downloads work
- [ ] All app features functional
- [ ] Performance meets expectations
- [ ] No data loss detected
- [ ] Backup verified and stored securely

## 🎯 Next Steps

After successful migration:
1. **Monitor system performance** for 48 hours
2. **Collect user feedback** on any issues
3. **Update documentation** with new project details
4. **Archive old project** (after retention period)
5. **Update CI/CD pipelines** with new project configuration
6. **Schedule post-migration review** with team

---

**Note**: This migration process is designed for the SIMDOC document management system. Adapt the procedures as needed for your specific use case.

## 📚 Additional Resources

### Migration Scripts Reference
- `config/setup-migration-config.js`: Initial configuration setup
- `data/firestore-migration.js`: Firestore data migration
- `data/auth-migration.js`: Authentication migration
- `data/storage-migration.js`: Storage file migration
- `data/backup-system.js`: Backup creation and management
- `orchestration/migration-orchestrator.js`: Main migration coordinator
- `verification/data-integrity-checker.js`: Data integrity verification

### Configuration Files
- `config/migration-config.json`: Main migration configuration
- `config/source-project.json`: Source project settings
- `config/target-project.json`: Target project settings
- `config/platform-configs/`: Platform-specific configuration templates
